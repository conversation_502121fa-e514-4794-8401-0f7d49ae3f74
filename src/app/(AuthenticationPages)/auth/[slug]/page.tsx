import Auth from "@/domain/auth/component/auth";
import type { Metadata } from "next";

export const generateMetadata = async ({
  params,
}: {
  params: Promise<{ slug: string }>;
}): Promise<Metadata> => {
  const { slug } = await params;
  const title = slug === "signin" ? "Login" : "Signup";

  return {
    title: `${title} | Markket`,
    description: `${title} to your Markket account to access your dashboard`,
    openGraph: {
      title: `${title} | Markket`,
      description: `${title} to your Markket account to access your dashboard`,
      type: "website",
      siteName: "Markket",
      locale: "en_US",
    },
    twitter: {
      card: "summary_large_image",
      title: `${title} | Markket`,
      description: `${title} to your Markket account to access your dashboard`,
    },
  };
};

export default function LoginPage() {
  return (
    <section className="">
      <Auth />
    </section>
  );
}
