import type { Metada<PERSON> } from "next";

export const metadata: Metadata = {
  title: "Markket - Authentication",
  description: "Login or sign up to access your Markket account",
};

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <main className="flex-1 w-full overflow-x-hidden">{children}</main>
    </div>
  );
}
