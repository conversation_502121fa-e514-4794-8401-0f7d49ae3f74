import { Lightbulb } from "lucide-react";

import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

const Page = () => {
  const post = {
    title: "How to Start Selling Successfully on Markket",
    authorName: "Chioma Okonkwo",
    image: "https://images.unsplash.com/photo-1472851294608-062f824d29cc",
    pubDate: new Date(),
    description:
      "Learn the essential steps to set up your store, optimize your listings, and start making sales on Markket&apos;s zero-fee marketplace platform.",
    authorImage: "https://images.unsplash.com/photo-1494790108377-be9c29b29330",
  };

  const { title, authorName, image, pubDate, description, authorImage } = post;
  return (
    <section className="py-32 flex flex-col items-center justify-center px-3 md:px-6">
      <div className="container  max-w-7xl ">
        <div className="mx-auto flex max-w-5xl flex-col items-center gap-4 text-center">
          <h1 className="max-w-3xl text-pretty text-5xl font-semibold md:text-6xl">
            {title}
          </h1>
          <h3 className="text-muted-foreground max-w-3xl text-lg md:text-xl">
            {description}
          </h3>
          <div className="flex items-center gap-3 text-sm md:text-base">
            <Avatar className="h-8 w-8 border">
              <AvatarImage src={authorImage} />
              <AvatarFallback>{authorName.charAt(0)}</AvatarFallback>
            </Avatar>
            <span>
              <a href="#" className="font-semibold">
                {authorName}
              </a>
              <span className="ml-1">
                on{" "}
                {new Date(pubDate).toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                })}
              </span>
            </span>
          </div>
          <img
            src={image}
            alt="marketplace selling"
            className="mb-8 mt-4 aspect-video w-full rounded-lg border object-cover"
          />
        </div>
      </div>
      <div className="container max-w-7xl">
        <div className="prose dark:prose-invert mx-auto max-w-3xl">
          <h2 className="text-3xl font-extrabold">
            Getting Started on Markket
          </h2>
          <p className="text-muted-foreground mt-2 text-lg">
            Starting your selling journey on Markket&apos;s is simple and
            straightforward. With our zero-fee platform, you can focus on
            growing your business without worrying about commission costs.
          </p>

          <h2>Setting Up Your Store</h2>
          <p>
            Creating a professional store presence is the first step to success.
            We&apos;ll guide you through optimizing your store profile, crafting
            compelling product descriptions, and setting competitive prices.
          </p>
          <Alert>
            <Lightbulb className="h-4 w-4" />
            <AlertTitle>Pro Tip!</AlertTitle>
            <AlertDescription>
              Complete your seller profile 100% to increase visibility and trust
              with potential buyers
            </AlertDescription>
          </Alert>
          <h2>Product Photography Tips</h2>
          <p>
            High-quality product photos are essential for success. Learn how to
            take professional-looking photos using just your smartphone and
            basic lighting techniques.
          </p>
          <div>
            <table>
              <thead>
                <tr>
                  <th>Feature</th>
                  <th>Benefit</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>Zero Fees</td>
                  <td>Keep more profits</td>
                </tr>
                <tr className="even:bg-muted m-0 border-t p-0">
                  <td>Local Reach</td>
                  <td>Target nearby customers</td>
                </tr>
                <tr className="even:bg-muted m-0 border-t p-0">
                  <td>Secure Payments</td>
                  <td>Safe transactions</td>
                </tr>
              </tbody>
            </table>
          </div>
          <p>
            With Markket&apos;s user-friendly platform, you can start selling
            your products to local customers quickly and efficiently, all while
            keeping 100% of your profits.
          </p>

          <h2>Marketing Your Products</h2>

          <img
            src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d"
            alt="digital marketing"
            className="my-8 aspect-video w-full rounded-md object-cover"
          />
          <p>
            Learn how to leverage Markket&apos;s built-in marketing tools to{" "}
            <a href="#">increase your visibility</a> and attract more customers
            to your store.
          </p>
          <blockquote>
            &ldquo;Success on Markket comes from understanding your local market
            and providing excellent customer service.&rdquo;
          </blockquote>
          <p>
            Here are the key areas to focus on when marketing your products:
          </p>
          <ul>
            <li>Optimize your product titles and descriptions</li>
            <li>Use high-quality images from multiple angles</li>
            <li>Respond promptly to customer inquiries</li>
          </ul>
          <p>
            By following these guidelines and consistently providing great
            service, you&apos;ll build a strong reputation and grow your
            business on Markket.
          </p>
        </div>
      </div>
    </section>
  );
};

export default Page;
