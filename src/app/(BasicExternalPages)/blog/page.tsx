import { ArrowRight } from "lucide-react";

import { Card } from "@/components/ui/card";

const Blog = () => {
  const posts = [
    {
      id: "post-1",
      title: "How to Start Selling Successfully on Markket",
      summary:
        "Learn the essential steps to set up your store, optimize your listings, and start making sales on Markket's zero-fee marketplace platform.",
      label: "Selling Guide",
      author: "Chioma Okonkwo",
      published: "15 Jun 2025",
      url: "/blog/start-selling",
      image:
        "https://images.unsplash.com/photo-1472851294608-062f824d29cc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2940&q=80",
      tags: ["Selling Tips", "E-commerce"],
    },
    {
      id: "post-2",
      title: "Smart Shopping: Making the Most of Local Marketplace",
      summary:
        "Discover how to find the best deals, connect with reliable sellers, and make secure transactions in your local marketplace on Markket.",
      label: "Shopping Guide",
      author: "Oluwaseun Adebayo",
      published: "22 Jun 2025",
      url: "/blog/smart-shopping",
      image:
        "https://images.unsplash.com/photo-1607082349566-187342175e2f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2940&q=80",
      tags: ["Shopping Tips", "Local Market"],
    },
  ];

  return (
    <section className="py-32 flex items-center justify-center  px-3 md:px-6">
      <div className="container flex flex-col items-center gap-16 max-w-7xl">
        <div className="text-center">
          <h2 className="mx-auto mb-6 text-2xl font-semibold text-pretty md:text-3xl lg:max-w-3xl">
            Markket Insights
          </h2>
          <p className="mx-auto max-w-2xl text-muted-foreground md:text-lg">
            Expert tips, success stories, and guides to help you make the most
            of your buying and selling experience on Markket.
          </p>
        </div>

        <div className="grid gap-y-10 sm:grid-cols-12 sm:gap-y-12 md:gap-y-16 lg:gap-y-20">
          {posts.map(post => (
            <Card
              key={post.id}
              className="order-last border-0 bg-transparent shadow-none sm:order-first sm:col-span-12 lg:col-span-10 lg:col-start-2"
            >
              <div className="grid gap-y-6 sm:grid-cols-10 sm:gap-x-5 sm:gap-y-0 md:items-center md:gap-x-8 lg:gap-x-12">
                <div className="sm:col-span-5">
                  <div className="mb-4 md:mb-6">
                    <div className="flex flex-wrap gap-3 text-xs tracking-wider text-muted-foreground uppercase md:gap-5 lg:gap-6">
                      {post.tags?.map(tag => <span key={tag}>{tag}</span>)}
                    </div>
                  </div>
                  <h3 className="text-xl font-semibold md:text-2xl lg:text-3xl">
                    <a
                      href={post.url}
                      target="_blank"
                      className="hover:underline"
                    >
                      {post.title}
                    </a>
                  </h3>
                  <p className="mt-4 text-muted-foreground md:mt-5">
                    {post.summary}
                  </p>
                  <div className="mt-6 flex items-center space-x-4 text-sm md:mt-8">
                    <span className="text-muted-foreground">{post.author}</span>
                    <span className="text-muted-foreground">•</span>
                    <span className="text-muted-foreground">
                      {post.published}
                    </span>
                  </div>
                  <div className="mt-6 flex items-center space-x-2 md:mt-8">
                    <a
                      href={post.url}
                      target="_blank"
                      className="inline-flex items-center font-semibold hover:underline md:text-base"
                    >
                      <span>Read more</span>
                      <ArrowRight className="ml-2 size-4 transition-transform" />
                    </a>
                  </div>
                </div>
                <div className="order-first sm:order-last sm:col-span-5">
                  <a href={post.url} target="_blank" className="block">
                    <div className="aspect-16/9 overflow-clip rounded-lg border border-border">
                      <img
                        src={post.image}
                        alt={post.title}
                        className="h-full w-full object-cover transition-opacity duration-200 fade-in hover:opacity-70"
                      />
                    </div>
                  </a>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Blog;
