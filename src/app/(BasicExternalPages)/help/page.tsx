import { ArrowRight, Search } from "lucide-react";
import Link from "next/link";

import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";

const Help = () => {
  const helpCategories = [
    {
      title: "Getting Started",
      description: "Learn the basics of using Markket",
      articles: [
        "Creating your account",
        "Understanding buyer and seller roles",
        "Navigating the marketplace",
      ],
      url: "/help/getting-started",
    },
    {
      title: "Buying Guide",
      description: "Everything you need to know about making purchases",
      articles: [
        "How to search for products",
        "Making secure payments",
        "Tracking your orders",
      ],
      url: "/help/buying",
    },
    {
      title: "Selling Guide",
      description: "Tips and instructions for successful selling",
      articles: [
        "Setting up your store",
        "Creating product listings",
        "Managing orders",
      ],
      url: "/help/selling",
    },
    {
      title: "Account & Security",
      description: "Manage your account settings and security",
      articles: [
        "Account verification",
        "Password and security",
        "Privacy settings",
      ],
      url: "/help/account",
    },
  ];

  return (
    <section className="py-32 flex items-center justify-center px-3 md:px-6">
      <div className="container flex flex-col items-center gap-16 max-w-7xl">
        <div className="text-center">
          <h1 className="mx-auto mb-6 text-3xl font-semibold text-pretty md:text-4xl lg:max-w-3xl">
            How can we help you?
          </h1>
          <p className="mx-auto max-w-2xl text-muted-foreground md:text-lg">
            Find answers to common questions and learn how to make the most of
            Markket.
          </p>
          <div className="mt-8 flex items-center gap-2 max-w-xl mx-auto">
            <Input
              type="search"
              placeholder="Search help articles..."
              className="h-12"
            />
            <Button size="icon" className="h-12 w-12">
              <Search className="h-5 w-5" />
            </Button>
          </div>
        </div>

        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-2">
          {helpCategories.map(category => (
            <Card
              key={category.title}
              className="p-6 transition-all hover:shadow-md"
            >
              <Link href={category.url}>
                <h2 className="text-xl font-semibold mb-2">{category.title}</h2>
                <p className="text-muted-foreground mb-4">
                  {category.description}
                </p>
                <ul className="space-y-2 mb-4">
                  {category.articles.map(article => (
                    <li key={article} className="flex items-center gap-2">
                      <ArrowRight className="h-4 w-4" />
                      <span>{article}</span>
                    </li>
                  ))}
                </ul>
                <Button variant="link" className="p-0">
                  View all articles <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </Card>
          ))}
        </div>

        <div className="text-center bg-muted p-8 rounded-lg">
          <h2 className="text-2xl font-semibold mb-4">Still need help?</h2>
          <p className="text-muted-foreground mb-6">
            Can&apos;t find what you&apos;re looking for? We&apos;re here to
            help.
          </p>
          <Button asChild>
            <Link href="/contact-us">Contact Support</Link>
          </Button>
        </div>
      </div>
    </section>
  );
};

export default Help;
