import type { Metada<PERSON> } from "next";
import Footer from "@/components/Footer";
import { BasicExternalPageNav } from "@/components/basic-external-page-nav";

export const metadata: Metadata = {
  title: "Markket",
  description: "Your next-gen product showcase platform",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex flex-col">
      <BasicExternalPageNav />
      <main className="flex-1 w-full overflow-x-hidden">{children}</main>
      <Footer />
    </div>
  );
}
