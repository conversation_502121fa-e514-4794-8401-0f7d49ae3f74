import type { Metadata } from "next";
import BecomeSeller from "@/domain/landingPage/components/become-seller";
import FaqSection from "@/domain/landingPage/components/faq";
import NewsletterSignup from "@/domain/landingPage/components/newsletter";
import CoreFeatures from "@/domain/landingPage/components/core-features";
import Hero from "@/domain/landingPage/components/hero";
import WhyUs from "@/domain/landingPage/components/why-us";
import Testimonials from "@/domain/landingPage/components/testimonials";

export const metadata: Metadata = {
  title: "Markket - Local Marketplace",
  description:
    "Discover Markket, the next-generation marketplace where sellers can showcase and sell their products with zero fees. Join our community of entrepreneurs today.",
  // keywords: "marketplace, zero-fee, e-commerce, online selling, product showcase",
  openGraph: {
    title: "Markket - Zero-Fee Marketplace Platform",
    description:
      "Discover Markket, the next-generation marketplace where sellers can showcase and sell their products with zero fees.",
    type: "website",
    // url: "https://markket.com",
    siteName: "Markket",
    locale: "en_US",
    // images: [{
    //   url: "/og-image.jpg",
    //   width: 1200,
    //   height: 630,
    //   alt: "Markket Platform Preview"
    // }],
  },
  // twitter: {
  //   card: "summary_large_image",
  //   title: "Markket - Zero-Fee Marketplace Platform",
  //   description: "Discover Markket, the next-generation marketplace where sellers can showcase and sell their products with zero fees.",
  //   images: ["/og-image.jpg"],
  // },
};

export default function LandingPage() {
  return (
    <div className="scroll-smooth">
      <Hero />
      <WhyUs />
      <CoreFeatures />
      <Testimonials />
      <FaqSection />
      <BecomeSeller />
      <NewsletterSignup />
    </div>
  );
}
