"use client";

import { <PERSON><PERSON><PERSON><PERSON> } from "lucide-react";
import { useState } from "react";

import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON>Footer,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";

const Pricing = () => {
  const [isYearly, setIsYearly] = useState(false);

  const plans = [
    {
      id: "plus",
      name: "Plus",
      description: "For personal use",
      monthlyPrice: "₦5,000",
      yearlyPrice: "₦50,000",
      features: [
        "List up to 50 products",
        "Basic analytics",
        "Community support",
        "Standard visibility",
      ],
      buttonText: "Get Started",
      buttonUrl: "/auth/signup",
    },
    {
      id: "pro",
      name: "Pro",
      description: "For growing businesses",
      monthlyPrice: "₦15,000",
      yearlyPrice: "₦150,000",
      features: [
        "Unlimited product listings",
        "Advanced analytics",
        "Priority support",
        "Featured visibility",
      ],
      buttonText: "Get Started",
      buttonUrl: "/auth/signup",
    },
  ];

  return (
    <section className="py-32 flex items-center justify-center  px-3 md:px-6">
      <div className="container max-w-7xl px-3 md:px-6">
        <div className="mx-auto flex max-w-5xl flex-col items-center gap-6 text-center">
          <h2 className="text-2xl font-semibold text-pretty lg:text-3xl">
            Choose Your Plan
          </h2>
          <p className="text-muted-foreground lg:text-lg">
            Select the perfect plan for your business needs
          </p>
          <div className="flex items-center gap-3 text-lg">
            Monthly
            <Switch
              checked={isYearly}
              onCheckedChange={() => setIsYearly(!isYearly)}
            />
            Yearly
          </div>
          <div className="flex flex-col items-stretch gap-6 md:flex-row">
            {plans.map(plan => (
              <Card
                key={plan.id}
                className="flex w-80 flex-col justify-between text-left"
              >
                <CardHeader>
                  <CardTitle>
                    <p>{plan.name}</p>
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">
                    {plan.description}
                  </p>
                  <div className="flex items-end">
                    <span className="text-4xl font-semibold">
                      {isYearly ? plan.yearlyPrice : plan.monthlyPrice}
                    </span>
                    <span className="text-2xl font-semibold text-muted-foreground">
                      {isYearly ? "/yr" : "/mo"}
                    </span>
                  </div>
                </CardHeader>
                <CardContent>
                  <Separator className="mb-6" />
                  {plan.id === "pro" && (
                    <p className="mb-3 font-semibold">
                      Everything in Plus, and:
                    </p>
                  )}
                  <ul className="space-y-4">
                    {plan.features.map((feature, index) => (
                      <li
                        key={index}
                        className="flex items-center gap-2 text-sm"
                      >
                        <CircleCheck className="size-4" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
                <CardFooter className="mt-auto">
                  <Button asChild className="w-full">
                    <a href={plan.buttonUrl}>{plan.buttonText}</a>
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Pricing;
