"use client";

import Link from "next/link";
import {
  Package2,
  ShoppingCart,
  Clock,
  Star,
  TrendingUp,
  Flame,
} from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { JSX } from "react";

const RequestsPage = () => {
  const categories = [
    "All",
    "Electronics",
    "Furniture",
    "Fashion",
    "Books",
    "Sports",
    "Home & Garden",
  ];

  const latestRequests = [
    {
      id: "REQ001",
      icon: <Package2 />,
      title: "Laptop Computer",
      category: "Electronics",
      description: "Looking for a high-performance laptop for programming.",
      status: "Pending",
      date: "2024-01-20",
      urgency: "High",
      budget: "₦500,000 - ₦800,000",
    },
    {
      id: "REQ002",
      icon: <ShoppingCart />,
      title: "Office Furniture",
      category: "Furniture",
      description: "Need ergonomic office chairs and desks for startup.",
      status: "Active",
      date: "2024-01-19",
      urgency: "Medium",
      budget: "₦200,000 - ₦400,000",
    },
  ];

  const trendingRequests = [
    {
      id: "REQ003",
      icon: <Star />,
      title: "Gaming Console",
      category: "Electronics",
      description: "Looking for latest gaming console with controllers.",
      status: "Active",
      date: "2024-01-18",
      urgency: "Low",
      budget: "₦300,000 - ₦450,000",
    },
  ];

  const urgentRequests = [
    {
      id: "REQ004",
      icon: <Flame />,
      title: "Professional Camera",
      category: "Electronics",
      description: "Urgent need for a professional DSLR camera for event.",
      status: "Urgent",
      date: "2024-01-21",
      urgency: "High",
      budget: "₦600,000 - ₦900,000",
    },
  ];

  interface Request {
    id: string;
    icon: JSX.Element;
    title: string;
    category: string;
    description: string;
    status: string;
    date: string;
    urgency: string;
    budget: string;
  }

  const RequestCard = ({ request }: { request: Request }) => (
    <div key={request.id}>
      <div className="grid items-center gap-4 px-4 py-4 md:grid-cols-4 w-full">
        <div className="order-2 flex items-center gap-2 md:order-none">
          <span className="flex h-12 w-14 shrink-0 items-center justify-center rounded-md bg-muted">
            {request.icon}
          </span>
          <div className="flex flex-col gap-1">
            <h3 className="text-sm font-semibold">{request.title}</h3>
            <p className="text-xs text-muted-foreground">{request.category}</p>
          </div>
        </div>
        <div className="order-1 md:order-none md:col-span-2">
          <p className="mb-2 text-sm">{request.description}</p>
          <div className="flex items-center gap-4 text-xs text-muted-foreground">
            <div className="flex items-center gap-2">
              <Clock className="h-3 w-3" />
              <span>Posted {request.date}</span>
            </div>
            <span className="font-medium">Budget: {request.budget}</span>
            <span
              className={`font-medium ${
                request.urgency === "High"
                  ? "text-red-500"
                  : request.urgency === "Medium"
                    ? "text-yellow-500"
                    : "text-green-500"
              }`}
            >
              Urgency: {request.urgency}
            </span>
          </div>
        </div>
        <div className="order-3 flex justify-end md:order-none">
          <Link href={`/requests/${request.id}`}>
            <Button variant="outline" size="sm">
              View Details
            </Button>
          </Link>
        </div>
      </div>
      <Separator />
    </div>
  );

  return (
    <section className="py-24 flex items-center justify-center  ">
      <div className="container px-3 md:px-6 min-h-screen">
        <div className="mx-auto max-w-7xl">
          <div className="mb-8 flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="text-2xl font-bold md:text-3xl">
                Product Requests
              </h1>
              <p className="text-muted-foreground">
                Browse buyer requests or create your own
              </p>
            </div>
            <Link href="/dashboard/buyer/requests/new">
              <Button>Create Request</Button>
            </Link>
          </div>

          <div className="mb-6 flex gap-2 overflow-x-auto">
            {categories.map(category => (
              <Button
                key={category}
                variant="outline"
                size="sm"
                className="whitespace-nowrap"
              >
                {category}
              </Button>
            ))}
          </div>

          <Tabs defaultValue="latest" className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="latest">Latest Requests</TabsTrigger>
              <TabsTrigger value="trending">
                <TrendingUp className="h-4 w-4 mr-2" />
                Trending
              </TabsTrigger>
              <TabsTrigger value="urgent">
                <Flame className="h-4 w-4 mr-2" />
                Urgent Requests
              </TabsTrigger>
            </TabsList>

            <div className="rounded-lg border">
              <TabsContent value="latest" className="flex flex-col w-full">
                {latestRequests.map(request => (
                  <RequestCard key={request.id} request={request} />
                ))}
              </TabsContent>

              <TabsContent value="trending" className="flex flex-col w-full">
                {trendingRequests.map(request => (
                  <RequestCard key={request.id} request={request} />
                ))}
              </TabsContent>

              <TabsContent value="urgent" className="flex flex-col w-full">
                {urgentRequests.map(request => (
                  <RequestCard key={request.id} request={request} />
                ))}
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </div>
    </section>
  );
};

export default RequestsPage;
