"use client";

import { useState } from "react";
import { Star, Shield, MapPin, Search } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";

const RidersPage = () => {
  const [searchQuery, setSearchQuery] = useState("");

  const trustedRiders = [
    {
      id: 1,
      name: "<PERSON><PERSON><PERSON><PERSON>",
      rating: 4.9,
      deliveries: 500,
      image: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e",
      location: "Road 1, OAU",
      status: "Available",
    },
    {
      id: 2,
      name: "<PERSON><PERSON>",
      rating: 4.8,
      deliveries: 450,
      image: "https://images.unsplash.com/photo-1494790108377-be9c29b29330",
      location: "Angola Hall, OAU",
      status: "On Delivery",
    },
    // Add more trusted riders
  ];

  return (
    <div className="py-32 px-4 md:px-6">
      <div className="container max-w-7xl mx-auto">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">Campus Delivery Riders</h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Connect with reliable and trusted riders for your campus delivery
            needs. Our riders are OAU students, verified, and committed to
            providing excellent service.
          </p>
        </div>

        {/* Search Section */}
        <div className="flex gap-4 max-w-xl mx-auto mb-12">
          <Input
            placeholder="Search riders by name or campus location..."
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
            className="flex-1"
          />
          <Button>
            <Search className="mr-2 h-4 w-4" />
            Search
          </Button>
        </div>

        {/* Most Trusted Riders Section */}
        <section className="mb-16">
          <h2 className="text-2xl font-semibold mb-6 flex items-center">
            <Shield className="mr-2" /> Top Riders
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {trustedRiders.map(rider => (
              <Card key={rider.id} className="p-6">
                <div className="flex items-start gap-4">
                  <Avatar className="h-16 w-16">
                    <AvatarImage
                      className="object-cover"
                      src={rider.image}
                      alt={rider.name}
                    />
                    <AvatarFallback>{rider.name[0]}</AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold">{rider.name}</h3>
                    <div className="flex items-center text-yellow-500">
                      <Star className="h-4 w-4 fill-current" />
                      <span className="ml-1">{rider.rating}</span>
                    </div>
                    <p className="text-sm text-gray-500">
                      {rider.deliveries} campus deliveries
                    </p>
                    <div className="flex items-center text-sm mt-2">
                      <MapPin className="h-4 w-4 mr-1" />
                      {rider.location}
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    className={`ml-auto ${
                      rider.status === "Available"
                        ? "text-green-500"
                        : "text-gray-500"
                    }`}
                  >
                    {rider.status}
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        </section>

        {/* Statistics Section */}
        <section className="bg-gray-50 p-8 rounded-lg mb-16">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <h3 className="text-3xl font-bold mb-2">100+</h3>
              <p className="text-gray-600">Active Campus Riders</p>
            </div>
            <div>
              <h3 className="text-3xl font-bold mb-2">99%</h3>
              <p className="text-gray-600">Campus Delivery Success Rate</p>
            </div>
            <div>
              <h3 className="text-3xl font-bold mb-2">4.9</h3>
              <p className="text-gray-600">Average Campus Rating</p>
            </div>
          </div>
        </section>

        {/* Rider Categories */}
        <section>
          <Tabs defaultValue="all" className="w-full">
            <TabsList className="grid w-full grid-cols-4 mb-8">
              <TabsTrigger value="all">All Riders</TabsTrigger>
              <TabsTrigger value="available">Available Now</TabsTrigger>
              <TabsTrigger value="express">Campus Express</TabsTrigger>
              <TabsTrigger value="specialized">Inter-Hall</TabsTrigger>
            </TabsList>
            <TabsContent value="all">{/* Grid of all riders */}</TabsContent>
            <TabsContent value="available">
              {/* Grid of available riders */}
            </TabsContent>
            <TabsContent value="express">
              {/* Grid of express delivery riders */}
            </TabsContent>
            <TabsContent value="specialized">
              {/* Grid of specialized riders */}
            </TabsContent>
          </Tabs>
        </section>
      </div>
    </div>
  );
};

export default RidersPage;
