"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card } from "@/components/ui/card";
import { ShoppingCart, Heart, Search, Plus } from "lucide-react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";

const ShopPage = () => {
  const [selectedCategory, setSelectedCategory] = useState("all");

  const bannerImages = [
    "https://images.unsplash.com/photo-1607082348824-0a96f2a4b9da?q=80&w=2940&auto=format&fit=crop",
    "https://images.unsplash.com/photo-1607082349566-187342175e2f?q=80&w=2940&auto=format&fit=crop",
    "https://images.unsplash.com/photo-1607083206869-4c7672e72a8a?q=80&w=2940&auto=format&fit=crop",
  ];

  const products = [
    {
      id: 1,
      name: "iPhone 15 Pro",
      price: "₦999,999",
      category: "Electronics",
      image:
        "https://images.unsplash.com/photo-1697644371824-41d4d0a8a12d?q=80&w=2940&auto=format&fit=crop",
    },
    {
      id: 2,
      name: "MacBook Pro M2",
      price: "₦1,499,999",
      category: "Electronics",
      image:
        "https://images.unsplash.com/photo-1517336714731-489689fd1ca8?q=80&w=2926&auto=format&fit=crop",
    },
    {
      id: 3,
      name: "AirPods Pro",
      price: "₦299,999",
      category: "Electronics",
      image:
        "https://images.unsplash.com/photo-1588156979435-379b9d802921?q=80&w=2940&auto=format&fit=crop",
    },
    {
      id: 4,
      name: "iPad Air",
      price: "₦799,999",
      category: "Electronics",
      image:
        "https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?q=80&w=2940&auto=format&fit=crop",
    },
  ];

  const vendors = [
    {
      id: 1,
      name: "Tech Store",
      logo: "https://images.unsplash.com/photo-1633409361618-c73427e4e206?q=80&w=2940&auto=format&fit=crop",
    },
    {
      id: 2,
      name: "Gadget Hub",
      logo: "https://images.unsplash.com/photo-1560179707-f14e90ef3623?q=80&w=2940&auto=format&fit=crop",
    },
    {
      id: 3,
      name: "Digital World",
      logo: "https://images.unsplash.com/photo-1478860409698-8707f313ee8b?q=80&w=2940&auto=format&fit=crop",
    },
    {
      id: 4,
      name: "Smart Shop",
      logo: "https://images.unsplash.com/photo-1541535650810-10d26f5c2ab3?q=80&w=2940&auto=format&fit=crop",
    },
  ];

  return (
    <div className="container py-32 max-w-7xl mx-auto px-3: md:px-0 py-8">
      {/* Search and Filter Section */}
      <div className="flex gap-4 mb-8">
        <Input placeholder="Search products..." className="flex-1" />
        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            <SelectItem value="electronics">Electronics</SelectItem>
            <SelectItem value="fashion">Fashion</SelectItem>
          </SelectContent>
        </Select>
        <Button>
          <Search className="mr-2 h-4 w-4" />
          Search
        </Button>
        <Button variant="outline">
          <ShoppingCart className="h-4 w-4" />
        </Button>
      </div>

      {/* Banner Carousel */}
      <Carousel className="mb-12">
        <CarouselContent>
          {bannerImages.map((image, index) => (
            <CarouselItem key={index}>
              <img
                src={image}
                alt={`Banner ${index + 1}`}
                className="w-full h-[400px] object-cover rounded-lg"
              />
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious />
        <CarouselNext />
      </Carousel>

      {/* Featured Sections */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
        <Card className="p-6">
          <h3 className="text-xl font-bold mb-4">New Arrival</h3>
          {/* Add content */}
        </Card>
        <Card className="p-6">
          <h3 className="text-xl font-bold mb-4">Holiday Sales</h3>
          {/* Add content */}
        </Card>
        <Card className="p-6">
          <h3 className="text-xl font-bold mb-4">iPhone Deals</h3>
          {/* Add content */}
        </Card>
      </div>

      {/* Sponsored Products */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Sponsored Products</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {products.map(product => (
            <Card key={product.id} className="p-4">
              <img
                src={product.image}
                alt={product.name}
                className="w-full h-48 object-cover rounded-md mb-4"
              />
              <h3 className="font-semibold">{product.name}</h3>
              <p className="text-muted-foreground">{product.category}</p>
              <p className="font-bold mt-2">{product.price}</p>
              <div className="flex justify-between mt-4">
                <Button variant="outline" size="icon">
                  <Heart className="h-4 w-4" />
                </Button>
                <Button>Add to Cart</Button>
              </div>
            </Card>
          ))}
        </div>
      </section>

      {/* Vendors Section */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Our Vendors</h2>
        <Input placeholder="Search vendors..." className="max-w-md mb-6" />
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {vendors.map(vendor => (
            <Card key={vendor.id} className="p-4 text-center">
              <img
                src={vendor.logo}
                alt={vendor.name}
                className="w-24 h-24 mx-auto rounded-full mb-4 object-cover"
              />
              <h3 className="font-semibold mb-4">{vendor.name}</h3>
              <Button variant="outline" className="w-full">
                View Products
              </Button>
            </Card>
          ))}
        </div>
      </section>

      {/* Product Request Section */}
      <section className="bg-muted p-8 rounded-lg text-center">
        <h2 className="text-2xl font-bold mb-4">
          Can&apos;t Find What You Want?
        </h2>
        <Button size="lg">
          <Plus className="mr-2 h-4 w-4" />
          Request for a Product
        </Button>
      </section>
    </div>
  );
};

export default ShopPage;
