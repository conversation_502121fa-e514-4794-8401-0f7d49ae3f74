import Link from "next/link";

import { Button } from "@/components/ui/button";

const Terms = () => {
  return (
    <section className="py-32 flex items-center justify-center px-3 md:px-6">
      <div className="container max-w-5xl">
        <div className="mx-auto flex max-w-5xl flex-col items-center gap-6 text-center">
          <h1 className="text-2xl font-semibold text-pretty lg:text-3xl">
            Terms of Service
          </h1>
          <p className="text-muted-foreground lg:text-lg">
            Last updated: June 23, 2025
          </p>
        </div>

        <div className="mt-16 prose prose-zinc dark:prose-invert mx-auto">
          <h2>1. Acceptance of Terms</h2>
          <p>
            By accessing and using Markket&apos;s services, you agree to be
            bound by these Terms of Service. If you do not agree to these terms,
            please do not use our services.
          </p>

          <h2>2. User Responsibilities</h2>
          <p>
            Users must be at least 18 years old to use our services. You are
            responsible for maintaining the confidentiality of your account
            information and for all activities under your account.
          </p>

          <h2>3. Listing and Selling</h2>
          <p>
            Sellers must accurately describe their items and fulfill orders as
            promised. Prohibited items include illegal goods, counterfeit
            products, and harmful materials.
          </p>

          <h2>4. Fees and Payments</h2>
          <p>
            Markket operates on a zero-fee model for basic listings. Premium
            features may incur charges, which will be clearly communicated
            before purchase.
          </p>

          <h2>5. Privacy Policy</h2>
          <p>
            Your privacy is important to us. Our Privacy Policy explains how we
            collect, use, and protect your personal information.
          </p>

          <div className="mt-16 border-t pt-16">
            <h2 className="text-3xl font-semibold mb-8">Privacy Policy</h2>

            <h3>Information Collection</h3>
            <p>
              We collect information you provide directly, including contact
              details, payment information, and listing content. We also collect
              usage data to improve our services.
            </p>

            <h3>Data Usage</h3>
            <p>
              Your data is used to provide and improve our services, process
              transactions, and communicate with you about your account and our
              platform.
            </p>

            <h3>Data Protection</h3>
            <p>
              We implement security measures to protect your personal
              information and maintain the trust you place in us.
            </p>
          </div>
        </div>

        <div className="mt-16 flex justify-center">
          <Button asChild>
            <Link href="/auth/signup">Create Account</Link>
          </Button>
        </div>
      </div>
    </section>
  );
};

export default Terms;
