"use client";

import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Package2, ShoppingCart, Plus, ArrowRight, Clock } from "lucide-react";

const Page = () => {
  const requests = [
    {
      id: "REQ001",
      icon: <Package2 />,
      title: "Laptop Computer",
      category: "Electronics",
      description: "Looking for a high-performance laptop for programming.",
      status: "Pending",
      date: "2024-01-20",
    },
    {
      id: "REQ002",
      icon: <ShoppingCart />,
      title: "Office Furniture",
      category: "Furniture",
      description: "Need ergonomic office chairs and desks for startup.",
      status: "Active",
      date: "2024-01-19",
    },
  ];

  return (
    <section className="w-full p-4">
      <div className="w-full">
        <div className="flex justify-between items-center mb-6">
          <div></div>
          <Button className="flex items-center gap-2 text-sm">
            <Plus className="h-3 w-3" />
            <span>Create New Request</span>
          </Button>
        </div>

        <div className="flex flex-col w-full">
          <Separator />
          {requests.map(request => (
            <React.Fragment key={request.id}>
              <div className="grid items-center gap-4 px-4 py-4 md:grid-cols-4 w-full">
                <div className="order-2 flex items-center gap-2 md:order-none">
                  <span className="flex h-12 w-14 shrink-0 items-center justify-center rounded-md bg-muted">
                    {request.icon}
                  </span>
                  <div className="flex flex-col gap-1">
                    <h3 className="text-sm font-semibold">{request.title}</h3>
                    <p className="text-xs text-muted-foreground">
                      {request.category}
                    </p>
                  </div>
                </div>
                <div className="order-1 md:order-none md:col-span-2">
                  <p className="mb-2 text-sm">{request.description}</p>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    <span>Posted: {request.date}</span>
                    <span
                      className={`px-2 py-0.5 rounded-full text-xs ${
                        request.status === "Active"
                          ? "bg-green-100 text-green-800"
                          : "bg-yellow-100 text-yellow-800"
                      }`}
                    >
                      {request.status}
                    </span>
                  </div>
                </div>
                <Button
                  variant="outline"
                  className="order-3 ml-auto w-fit gap-2 md:order-none text-sm"
                >
                  <span>View Details</span>
                  <ArrowRight className="h-3 w-3" />
                </Button>
              </div>
              <Separator />
            </React.Fragment>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Page;
