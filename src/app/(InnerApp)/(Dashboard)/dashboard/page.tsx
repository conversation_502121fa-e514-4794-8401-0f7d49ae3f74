"use client";
// import { useRouter } from "next/navigation";
// import { useAuthContext } from "@/context/AuthContext";
import { SectionCards } from "@/domain/dashboard-analytics/components/section-cards";
import { ChartAreaInteractive } from "@/domain/dashboard-analytics/components/chart-area-interactive";
import { DataTable } from "@/domain/dashboard-analytics/components/data-table";
import data from "@/domain/dashboard-analytics/static/data.json";

export default function DashboardRedirectPage() {
  // const { user } = useAuthContext();
  // const router = useRouter();

  return (
    <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
      <SectionCards />
      <div className="px-4 lg:px-6">
        <ChartAreaInteractive />
      </div>
      <DataTable data={data} />
    </div>
  );
}
