"use client";
import React, { useState } from "react";
import {
  Check,
  AlertCircle,
  Store,
  Truck,
  Rocket,
  Star,
  ClipboardCheck,
} from "lucide-react";
import { StoreData } from "@/domain/seller-store/types";
import BasicInfoStep from "@/domain/seller-store/components/basic-info-step";
import { BusinessDetailsStep } from "@/domain/seller-store/components/business-details-step";
import { PoliciesStep } from "@/domain/seller-store/components/policies-step";
import { CategoriesStep } from "@/domain/seller-store/components/categories-step";
import { LaunchStep } from "@/domain/seller-store/components/launch-step";
import { WelcomeScreen } from "@/domain/seller-store/components/welcome-screen";
import { ReviewStep } from "@/domain/seller-store/components/review-step";


const Page = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [storeData, setStoreData] = useState<StoreData>({
    basicInfo: {
      storeName: "",
      description: "",
      category: "",
      businessType: "",
      tagline: "",
      logo: null,
      banner: null,
    },
    businessDetails: {
      legalName: "",
      registrationNumber: "",
      taxId: "",
      address: "",
      phone: "",
      email: "",
      documents: {
        businessLicense: null,
        taxCertificate: null,
        idVerification: null,
      },
    },
    policies: {
      shippingZones: [],
      processingTime: "",
      returnWindow: "",
      returnConditions: "",
      refundTime: "",
      customPolicies: "",
    },
    categories: {
      mainCategories: [],
      collections: [],
    },
  });

  const steps = [
    { title: "Welcome", icon: Star },
    { title: "Basic Info", icon: Store },
    { title: "Business Details", icon: AlertCircle },
    { title: "Policies", icon: Truck },
    { title: "Review", icon: ClipboardCheck },
    { title: "Launch", icon: Rocket },
  ];

  const updateStoreData = (section: keyof StoreData, data: Partial<StoreData[keyof StoreData]>) => {
    setStoreData(prev => ({
      ...prev,
      [section]: { ...prev[section], ...data },
    }));
  };

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return <WelcomeScreen onNext={nextStep} />;
      case 1:
        return (
          <BasicInfoStep
            data={storeData.basicInfo}
            onUpdate={data => updateStoreData("basicInfo", data)}
            onNext={nextStep}
            onPrev={prevStep}
          />
        );
      case 2:
        return (
          <BusinessDetailsStep
            data={storeData.businessDetails}
            onUpdate={data => updateStoreData("businessDetails", data)}
            onNext={nextStep}
            onPrev={prevStep}
          />
        );
      case 3:
        return (
          <PoliciesStep
            data={storeData.policies}
            onUpdate={data => updateStoreData("policies", data)}
            onNext={nextStep}
            onPrev={prevStep}
          />
        );
      case 4:
        return (
          <CategoriesStep
            data={storeData.categories}
            onUpdate={data => updateStoreData("categories", data)}
            onNext={nextStep}
            onPrev={prevStep}
          />
        );
      case 5:
        return <ReviewStep data={storeData} onNext={nextStep} onPrev={prevStep} />;
      case 6:
        return <LaunchStep data={storeData} onPrev={prevStep} />;
      default:
        return <WelcomeScreen onNext={nextStep} />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Progress Header */}
      <div className="bg-white border-b">
        <div className="max-w-3xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-2xl font-bold text-gray-900">
              Create Your Store
            </h1>
            <span className="text-sm text-gray-500">
              Step {currentStep + 1} of {steps.length}
            </span>
          </div>
          <div className="flex items-center justify-center">
            <div className="flex items-center space-x-2">
              {steps.map((step, index) => {
                const Icon = step.icon;
                return (
                  <div key={index} className="flex items-center">
                    <div
                      className={`flex items-center justify-center w-8 h-8 rounded-full ${
                        index <= currentStep
                          ? "bg-black text-white"
                          : "bg-gray-200 text-gray-400"
                      }`}
                    >
                      {index < currentStep ? (
                        <Check className="w-4 h-4" />
                      ) : (
                        <Icon className="w-4 h-4" />
                      )}
                    </div>
                    {index < steps.length - 1 && (
                      <div
                        className={`w-8 h-0.5 ${index < currentStep ? "bg-black" : "bg-gray-200"}`}
                      />
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Step Content */}
      <div className="max-w-4xl mx-auto px-4 py-8">{renderStep()}</div>
    </div>
  );
};





export default Page;
