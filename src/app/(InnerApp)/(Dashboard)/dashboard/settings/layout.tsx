"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";

export default function SettingsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  const navItems = [
    { name: "Profile", href: "/dashboard/settings/profile" },
    { name: "Security", href: "/dashboard/settings/security" },
    { name: "Notifications", href: "/dashboard/settings/notifications" },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex flex-col md:flex-row">
        <div className="w-full md:w-64 bg-white border-b md:border-b-0 md:border-r border-gray-200 md:min-h-screen">
          <div className="p-4 md:p-6">
            <h1 className="text-xl md:text-2xl font-semibold text-gray-900 mb-2">
              Settings
            </h1>
            <p className="text-sm text-gray-600 mb-4 md:mb-8">
              Manage your account settings and set email preferences.
            </p>

            <nav className="space-y-1">
              {navItems.map(item => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`block px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    pathname === item.href
                      ? "bg-gray-100 text-gray-900"
                      : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                  }`}
                >
                  {item.name}
                </Link>
              ))}
            </nav>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-4 md:p-8">
          <div className="max-w-2xl">{children}</div>
        </div>
      </div>
    </div>
  );
}
