"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";

type Preferences = {
  notificationType: "all" | "mentions" | "nothing";
  timeFrom: string;
  timeTo: string;
  infoMethod: "mobile" | "email";
};

const defaultPreferences: Preferences = {
  notificationType: "all",
  timeFrom: "00:00",
  timeTo: "00:00",
  infoMethod: "mobile",
};

const NotificationPage = () => {
  const [prefs, setPrefs] = useState<Preferences>(defaultPreferences);
  const [initialPrefs] =
    useState<Preferences>(defaultPreferences);

  const hasChanges = JSON.stringify(prefs) !== JSON.stringify(initialPrefs);

  return (
    <div>
      <Card className="border-none shadow-none bg-transparent py-0">
        <CardHeader className="p-0">
          <CardTitle className="text-base">
            Your Notification Preferences
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Manage when and why you get notified.
          </p>
        </CardHeader>
        <CardContent className="space-y-6 p-0">
          <div>
            <Label className="text-sm font-semibold">
              Send notifications for:
            </Label>
            <RadioGroup
              value={prefs.notificationType}
              onValueChange={value =>
                setPrefs(prev => ({
                  ...prev,
                  notificationType: value as Preferences["notificationType"],
                }))
              }
              className="mt-2 space-y-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="all" id="all" />
                <Label htmlFor="all">All new messages</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="mentions" id="mentions" />
                <Label htmlFor="mentions">Mentions</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="nothing" id="nothing" />
                <Label htmlFor="nothing">Nothing</Label>
              </div>
            </RadioGroup>
          </div>

          <Separator />

          <div>
            <Label className="text-sm font-semibold">
              Receive notifications only within:
            </Label>
            <div className="mt-2 flex items-center gap-4">
              <div className="space-y-1">
                <Label className="text-sm">From</Label>
                <Input
                  type="time"
                  value={prefs.timeFrom}
                  onChange={e =>
                    setPrefs(prev => ({ ...prev, timeFrom: e.target.value }))
                  }
                />
              </div>
              <div className="space-y-1">
                <Label className="text-sm">To</Label>
                <Input
                  type="time"
                  value={prefs.timeTo}
                  onChange={e =>
                    setPrefs(prev => ({ ...prev, timeTo: e.target.value }))
                  }
                />
              </div>
            </div>
            <p className="mt-2 text-xs text-muted-foreground">
              <span className="font-medium text-foreground">Note:</span> Outside
              this time, notifications are paused.
            </p>
          </div>

          <Separator />

          <div>
            <Label className="text-sm font-semibold">
              To keep me informed:
            </Label>
            <RadioGroup
              value={prefs.infoMethod}
              onValueChange={value =>
                setPrefs(prev => ({
                  ...prev,
                  infoMethod: value as Preferences["infoMethod"],
                }))
              }
              className="mt-2 space-y-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="mobile" id="mobile" />
                <Label htmlFor="mobile">
                  Use different settings for mobile devices
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="email" id="email" />
                <Label htmlFor="email">Receive notifications via email</Label>
              </div>
            </RadioGroup>
          </div>

          {hasChanges && (
            <div className="flex justify-end space-x-3 pt-4">
              <Button variant="outline">Revert Changes</Button>
              <Button>{"Save Changes"}</Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default NotificationPage;
