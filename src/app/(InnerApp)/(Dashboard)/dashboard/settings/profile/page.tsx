"use client";

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import ProfileForm from "@/domain/settings/components/profile-form";
import { Camera } from "lucide-react";
import { useEffect, useRef, useState } from "react";

async function getProfileData() {
  return {
    name: "<PERSON> Ikechukwu",
    email: "",
    phone: "",
    role: "Buyer",
    avatar: "", // Add avatar URL field
  };
}

const getInitials = (name: string) => {
  return name
    .split(" ")
    .map(n => n[0])
    .join("")
    .toUpperCase();
};

export default function ProfilePage() {
  const [avatarUrl, setAvatarUrl] = useState("");
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [initialData, setInitialData] = useState({
    name: "",
    email: "",
    phone: "",
    role: "",
    avatar: "",
  });

  useEffect(() => {
    const fetchProfileData = async () => {
      const data = await getProfileData();
      setInitialData(data);
    };
    fetchProfileData();
  }, []);

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      // Here you would typically:
      // 1. Upload the file to your server/storage
      // 2. Get back the URL
      // 3. Update the avatar URL in your database
      // For now, we'll just create a local URL
      const localUrl = URL.createObjectURL(file);
      setAvatarUrl(localUrl);
    }
  };

  const handleAvatarClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="">
      <div className="flex items-center gap-4 mb-8">
        <div className="relative group">
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            accept="image/*"
            className="hidden"
          />
          <Avatar className="w-16 h-16 bg-gray-200">
            {avatarUrl || initialData.avatar ? (
              <AvatarImage
                src={avatarUrl || initialData.avatar}
                alt={initialData.name}
              />
            ) : (
              <AvatarFallback className="text-gray-600 text-lg font-medium">
                {getInitials(initialData.name)}
              </AvatarFallback>
            )}
          </Avatar>
          <div
            onClick={handleAvatarClick}
            className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
          >
            <Camera className="text-white size-4" />
          </div>
        </div>
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">
            {initialData.name}
          </h1>
          <p className="text-gray-600">{initialData.role}</p>
        </div>
      </div>

      <ProfileForm initialData={initialData} />
    </div>
  );
}
