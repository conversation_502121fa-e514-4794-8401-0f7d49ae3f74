"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Laptop, Smartphone, LogOut } from "lucide-react";
import { useState } from "react";

export default function SecurityPage() {
  const [devices] = useState([
    {
      id: 1,
      type: "Desktop",
      name: "MacBook Pro",
      location: "Lagos, Nigeria",
      lastActive: "Active now",
      icon: Laptop,
      isCurrent: true,
    },
    {
      id: 2,
      type: "Mobile",
      name: "iPhone 13",
      location: "Lagos, Nigeria",
      lastActive: "2 hours ago",
      icon: Smartphone,
      isCurrent: false,
    },
  ]);

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Security Settings</h3>
        <p className="text-sm text-muted-foreground">
          Manage your logged in devices and security preferences
        </p>
      </div>

      <div className="space-y-4">
        <h4 className="text-sm font-medium">Active Sessions</h4>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Device</TableHead>
              <TableHead>Location</TableHead>
              <TableHead>Last Active</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {devices.map(device => (
              <TableRow key={device.id}>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <device.icon className="h-4 w-4" />
                    {device.name}
                    {device.isCurrent && (
                      <span className="text-xs text-green-500">(Current)</span>
                    )}
                  </div>
                </TableCell>
                <TableCell>{device.location}</TableCell>
                <TableCell>{device.lastActive}</TableCell>
                <TableCell>
                  {!device.isCurrent && (
                    <Button variant="destructive" size="sm">
                      <LogOut className="h-4 w-4 mr-2" />
                      Sign Out
                    </Button>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
