import Link from "next/link";
import Image from "next/image";
import { asset } from "public/images";

export default async function NotFound() {
  return (
    <div className="flex ">
      <div className="w-full h-screen flex flex-col">
        <div className="max-w-full mt-24 flex flex-col items-center justify-center">
          <Image
            src={asset.error404}
            height="100"
            width="100"
            alt="error image"
            className="w-60 h-40 object-contain"
          />
          <p className="text-xl font-semibold leading-6 mt-5 text-slate-600">
            Oops! Page Not Found
          </p>
          <p className="text-sm font-normal leading-5 mt-3 text-slate-600">
            {"The page you're looking for doesn't exist or has been moved."}
          </p>
          <Link
            href="/"
            className="mt-6 px-6 py-2 bg-[#d03417] text-white rounded-full hover:bg-[#b02e14] transition-colors duration-300"
          >
            Return to Home
          </Link>
        </div>
      </div>
    </div>
  );
}
