"use client";

import Link from "next/link";
import Image from "next/image";
import { asset } from "../../../public/images";
import {
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
  Mail,
  Phone,
  MapPin,
} from "lucide-react";

// Footer navigation items
const footerLinks = [
  {
    title: "Company",
    links: [
      { label: "About Us", href: "/about" },
      { label: "Careers", href: "/careers" },
      { label: "Blog", href: "/blog" },
      { label: "Press", href: "/press" },
    ],
  },
  {
    title: "Products",
    links: [
      { label: "Shop", href: "/shop" },
      { label: "Sell", href: "/sell" },
      { label: "Deliver", href: "/deliver" },
      { label: "Pricing", href: "/pricing" },
    ],
  },
  {
    title: "Support",
    links: [
      { label: "Help Center", href: "/help" },
      { label: "Contact Us", href: "/contact" },
      { label: "Privacy Policy", href: "/privacy" },
      { label: "Terms of Service", href: "/terms" },
    ],
  },
];

export default function Footer() {
  return (
    <footer className="bg-[#2A252B] text-white w-screen left-[calc(-50vw+50%)] right-[calc(-50vw+50%)] relative">
      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto py-16 px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-10">
          {/* Logo and Company Info */}
          <div className="space-y-6">
            <Link href="/" className="inline-block">
              <div className="relative w-32 h-9">
                <Image
                  src={asset.logo}
                  alt="Markket"
                  fill
                  className="object-contain"
                />
              </div>
            </Link>
            <p className="text-white/70 text-sm">
              Connecting buyers, sellers, and delivery services in one seamless
              marketplace.
            </p>
            <div className="flex space-x-4">
              <Link
                href="https://facebook.com"
                className="text-white/70 hover:text-white transition-colors"
              >
                <Facebook size={20} />
              </Link>
              <Link
                href="https://twitter.com"
                className="text-white/70 hover:text-white transition-colors"
              >
                <Twitter size={20} />
              </Link>
              <Link
                href="https://instagram.com"
                className="text-white/70 hover:text-white transition-colors"
              >
                <Instagram size={20} />
              </Link>
              <Link
                href="https://linkedin.com"
                className="text-white/70 hover:text-white transition-colors"
              >
                <Linkedin size={20} />
              </Link>
            </div>
          </div>

          {/* Navigation Links */}
          {footerLinks.map(section => (
            <div key={section.title}>
              <h3 className="font-semibold text-lg mb-4">{section.title}</h3>
              <ul className="space-y-3">
                {section.links.map(link => (
                  <li key={link.label}>
                    <Link
                      href={link.href}
                      className="text-white/70 hover:text-white transition-colors text-sm"
                    >
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Contact Information */}
        <div className="mt-12 pt-8 border-t border-white/10">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex items-center space-x-3">
              <Mail size={18} className="text-[#e9cc77]" />
              <span className="text-sm"><EMAIL></span>
            </div>
            <div className="flex items-center space-x-3">
              <Phone size={18} className="text-[#e9cc77]" />
              <span className="text-sm">+234 ************</span>
            </div>
            <div className="flex items-center space-x-3">
              <MapPin size={18} className="text-[#e9cc77]" />
              <span className="text-sm">Ile-Ife, Osun State, Nigeria</span>
            </div>
          </div>
        </div>
      </div>

      {/* Copyright Bar */}
      <div className="bg-black/30 py-4">
        <div className="max-w-7xl mx-auto px-4 flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-white/60">
            &copy; {new Date().getFullYear()} Markket. All rights reserved.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <Link
              href="/privacy"
              className="text-sm text-white/60 hover:text-white transition-colors"
            >
              Privacy Policy
            </Link>
            <Link
              href="/terms"
              className="text-sm text-white/60 hover:text-white transition-colors"
            >
              Terms of Service
            </Link>
            <Link
              href="/cookies"
              className="text-sm text-white/60 hover:text-white transition-colors"
            >
              Cookie Policy
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
