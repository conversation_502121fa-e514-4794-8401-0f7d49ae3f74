import React, { JSX } from "react";

const Logo: React.FC<React.SVGProps<SVGSVGElement>> = props => {
  return (
    <svg
      width="476"
      height="134"
      viewBox="0 0 476 134"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M64.5951 74.6793L18.3031 14.2673L17.2217 15.0944L76.4556 92.3813L94.2983 62.8818L100.867 118.917L128.707 114.759L111.037 0L64.5951 74.6793Z"
        fill="currentColor"
      />
      <path
        d="M17.3173 20.8812L16.2402 21.7061L73.8563 96.7115L74.9334 95.8865L17.3173 20.8812Z"
        fill="currentColor"
      />
      <path
        d="M16.3471 27.4966L15.271 28.3229L71.2747 101.045L72.3507 100.219L16.3471 27.4966Z"
        fill="currentColor"
      />
      <path
        d="M15.3616 34.099L14.2866 34.9268L68.6777 105.351L69.7526 104.523L15.3616 34.099Z"
        fill="currentColor"
      />
      <path
        d="M14.3897 40.7143L13.3159 41.5436L66.0951 109.684L67.1689 108.855L14.3897 40.7143Z"
        fill="currentColor"
      />
      <path
        d="M0 134L27.8396 129.83L33.9367 75.9659L63.5019 114.023L64.5718 113.185L12.8154 46.9603L0 134Z"
        fill="currentColor"
      />
      <path
        d="M179.842 64.8921C176.183 63.6859 172.042 63.0771 167.394 63.0771C162.448 63.0771 157.881 63.6629 153.693 64.8461C149.506 66.0293 145.928 67.27 142.96 68.5795L148.781 79.0675C149.54 78.6539 150.76 78.114 152.439 77.4592C154.107 76.8045 156.074 76.2301 158.318 75.7476C160.561 75.2652 162.862 75.0239 165.22 75.0239C166.819 75.0239 168.246 75.1618 169.5 75.4375C170.753 75.7132 171.8 76.1612 172.64 76.793C173.48 77.4133 174.101 78.16 174.527 79.0215C174.941 79.8831 175.159 80.9054 175.159 82.0886V86.3045C175.044 86.2471 174.941 86.1896 174.814 86.1322C172.985 85.3051 170.799 84.6044 168.246 84.053C165.692 83.5016 162.827 83.2259 159.629 83.2259C153.083 83.2259 148.022 84.5584 144.444 87.2235C140.866 89.8886 139.083 93.5071 139.083 98.079C139.083 101.33 139.934 104.098 141.648 106.384C143.362 108.67 145.64 110.382 148.505 111.519C151.358 112.668 154.464 113.231 157.811 113.231C161.159 113.231 164.277 112.691 167.176 111.623C170.063 110.554 172.422 108.958 174.251 106.844C174.596 106.453 174.883 106.04 175.159 105.615V112.197H191.714V78.5505C191.714 75.2307 190.644 72.4278 188.516 70.1418C186.387 67.8558 183.488 66.1097 179.842 64.9036V64.8921ZM173.56 98.4811C172.491 100.009 171.122 101.146 169.454 101.904C167.774 102.662 165.956 103.042 163.978 103.042C162.609 103.042 161.355 102.812 160.204 102.364C159.065 101.916 158.145 101.261 157.466 100.388C156.776 99.5265 156.443 98.4696 156.443 97.2175C156.443 96.0458 156.742 95.0579 157.351 94.2538C157.961 93.4611 158.87 92.8523 160.089 92.4388C161.309 92.0252 162.942 91.8184 165.001 91.8184C167.659 91.8184 170.201 92.1516 172.594 92.8063C173.48 93.0476 174.308 93.3463 175.113 93.6679C174.998 95.5289 174.492 97.1371 173.56 98.4696V98.4811Z"
        fill="currentColor"
      />
      <path
        d="M240.238 64.3177C238.489 63.6285 236.66 63.2838 234.762 63.2838C231.863 63.2838 229.09 64.1454 226.421 65.88C224.569 67.0862 222.982 68.614 221.624 70.4175V64.4326H204.61V112.197H221.624V88.625C221.624 86.339 221.969 84.4091 222.648 82.8123C223.338 81.2156 224.328 80.0094 225.616 79.1823C226.905 78.3553 228.4 77.9417 230.068 77.9417C231.587 77.9417 232.864 78.1829 233.887 78.6654C234.911 79.1479 235.889 79.8027 236.798 80.6412L244.218 67.247C243.309 66.0063 241.975 65.0299 240.226 64.3407L240.238 64.3177Z"
        fill="currentColor"
      />
      <path
        d="M307.593 64.4211H287.047L267.974 81.1467V31.1882H251.419V112.186H267.974V87.9702L289.67 112.186H309.883L285.218 84.1449L307.593 64.4211Z"
        fill="currentColor"
      />
      <path
        d="M373.477 64.4211H352.919L333.857 81.1467V31.1882H317.303V112.186H333.857V87.9702L355.553 112.186H375.754L351.09 84.1449L373.477 64.4211Z"
        fill="currentColor"
      />
      <path
        d="M415.892 100.342C413.717 101.376 411.117 101.904 408.069 101.904C405.4 101.904 403.157 101.364 401.328 100.296C399.498 99.2278 398.129 97.654 397.221 95.5748C396.703 94.4031 396.346 93.0706 396.128 91.6232H436.162C436.311 91.1407 436.392 90.5319 436.392 89.8081V87.5796C436.392 82.4562 435.287 78.091 433.079 74.4955C430.87 70.9 427.672 68.143 423.484 66.2361C419.297 64.3292 414.155 63.3757 408.069 63.3757C401.983 63.3757 396.703 64.3751 392.251 66.3854C387.799 68.3957 384.394 71.2676 382.035 75.0009C379.677 78.7343 378.492 83.1685 378.492 88.2918C378.492 93.4152 379.712 97.7344 382.15 101.479C384.589 105.213 388.029 108.108 392.481 110.152C396.933 112.197 402.202 113.219 408.299 113.219C414.994 113.219 420.689 112.048 425.371 109.693C430.053 107.338 433.757 103.811 436.507 99.1014L421.091 95.6782C419.791 97.7574 418.066 99.3082 415.892 100.354V100.342ZM401.224 75.426C402.973 74.3921 405.147 73.8637 407.735 73.8637C409.794 73.8637 411.578 74.2772 413.096 75.1043C414.615 75.9314 415.8 77.1146 416.639 78.6309C417.318 79.8601 417.709 81.296 417.836 82.8928H396.461C396.68 81.9049 396.945 80.9629 397.29 80.1358C398.164 78.0221 399.475 76.4484 401.224 75.4145V75.426Z"
        fill="currentColor"
      />
      <path
        d="M476 64.4211H466.636V47.8104H450.082V64.4211H440.833V77.4018H450.082V112.186H466.636V77.4018H476V64.4211Z"
        fill="currentColor"
      />
    </svg>
  );
};

type IconType = "logo";

type Icon = {
  name: IconType;
  svgProps: React.SVGProps<SVGSVGElement>;
};

const Icons: React.FC<Icon> = ({ name, svgProps }) => {
  const icons: Record<IconType, JSX.Element> = {
    logo: <Logo {...svgProps} />,
  };

  return icons[name];
};

export default Icons;
