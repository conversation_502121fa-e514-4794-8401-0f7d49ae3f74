// src/context/AuthContext.tsx

"use client";

import { createContext, useContext } from "react";
import { useAuth } from "@/hooks/useAuth"; // ✅ use the real one now
import { ProgressProvider } from "@bprogress/next/app";

const AuthContext = createContext<ReturnType<typeof useAuth> | null>(null);

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const auth = useAuth();
  return (
    <AuthContext.Provider value={auth}>
      <ProgressProvider color="#d03417">{children}</ProgressProvider>
    </AuthContext.Provider>
  );
};

export const useAuthContext = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuthContext must be used within an AuthProvider");
  }
  return context;
};
