"use client";

import Link from "next/link";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { AuthAsset } from "public/images/auth";
import { Input } from "@/components/ui/input";
import { useRouter } from "@bprogress/next/app";
import Icons from "@/components/ui/icons";

function Auth() {
  const [email, setEmail] = useState("");
  const [errors, setErrors] = useState({ email: "", password: "" });
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const router = useRouter();

  const carouselImages = [
    AuthAsset.loginModel1,
    AuthAsset.loginModel2,
    AuthAsset.loginModel3,
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex(
        prevIndex => (prevIndex + 1) % carouselImages.length
      );
    }, 4000);

    return () => clearInterval(interval);
  }, [carouselImages.length]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    router.push("/dashboard"); // Redirect to the inner dashboard
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    setErrors(prevErrors => ({ ...prevErrors, email: "" }));
  };

  return (
    <>
      <main className="w-full min-h-screen grid grid-cols-1 lg:grid-cols-2 ">
        <section className="w-full col-span-1 bg-[#F9FAFB] px-[20px] md:px-[40px] py-[20px] hidden lg:block">
          <Link href="/">
            <Icons name="logo" svgProps={{ width: 100, height: 25 }} />
          </Link>
          <div className="h-fit flex-1 flex flex-col">
            <div className="w-full flex flex-col gap-[12px] mb-[40px] ">
              <h1 className="w-full text-center mt-[40px] md:mt-[70px] text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px]">
                Your Ultimate{" "}
                <span className="text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px] bg-[#d03417] bg-clip-text text-transparent">
                  Marketplace
                </span>{" "}
                Experience!
              </h1>
              <p className="w-full text-center text-[12px] md:text-[14px] text-[#344054] font-[400] leading-[18px] md:leading-[21px] px-[20px] md:px-[5%]">
                Discover a world of endless possibilities where buyers and
                sellers connect seamlessly. Our platform empowers entrepreneurs
                and shoppers alike, creating a vibrant community where quality
                products meet exceptional value.
              </p>
            </div>
            <div className="h-[72dvh] text-center">
              <section className="w-full h-[72dvh] bg-black rounded-lg relative overflow-hidden">
                {carouselImages.map((imageSrc, index) => (
                  <div
                    key={index}
                    className={`absolute inset-0 w-full h-full transition-opacity duration-1000 ease-in-out ${
                      index === currentImageIndex ? "opacity-100" : "opacity-0"
                    }`}
                  >
                    <Image
                      src={imageSrc}
                      alt={
                        index === 0
                          ? "Seller showcasing her products on Markket platform"
                          : index === 1
                            ? "Satisfied customer receiving their purchased product"
                            : "Delivery rider ensuring safe and timely product delivery"
                      }
                      width={1920}
                      height={2880}
                      quality={100}
                      priority={index === 0}
                      className="w-full h-full object-cover rounded-lg"
                    />
                    <div className="absolute bottom-12 left-1/2 transform -translate-x-1/2 text-white text-sm bg-black/50 px-4 py-2 rounded-md">
                      {index === 0
                        ? "Empowering sellers to showcase their products"
                        : index === 1
                          ? "Delighting customers with quality products"
                          : "Swift and reliable delivery service"}
                    </div>
                  </div>
                ))}

                {/* Optional: Carousel indicators */}
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                  {carouselImages.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentImageIndex(index)}
                      className={`w-2 h-2 rounded-sm transition-colors duration-300 ${
                        index === currentImageIndex ? "bg-white" : "bg-white/50"
                      }`}
                    />
                  ))}
                </div>
              </section>
            </div>
          </div>
        </section>
        <section className="w-full col-span-1  justify-center items-center flex flex-col lg:px-[40px] max-w-full p-3 md:p-0 md:max-w-lg mx-auto">
          <Link href="/" className="block md:hidden">
            <Icons name="logo" svgProps={{ width: 100, height: 25 }} />
          </Link>
          <div className="w-full flex flex-col justify-center mt-[60px] md:mt-[80px] items-center gap-[8px] mb-[32px]">
            <h1 className="w-full text-center text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px]">
              Welcome to Markket
            </h1>
            <p className="w-full text-center text-[14px] md:text-[16px] text-[#344054] font-[400] leading-[21px] md:leading-[27px]">
              Sign up to unlock all features
            </p>
          </div>
          <div className="flex flex-col items-center justify-center gap-4 w-full">
            <div className="border border-gray-400 rounded-md p-3 w-full grid grid-cols-12 cursor-pointer hover:bg-gray-200 transition-all duration-300 ease-in-out transform hover:scale-[101%] hover:shadow-md">
              <Image
                src={AuthAsset.google}
                alt="facebook"
                width={24}
                height={24}
                className="col-span-1"
              />
              <p className="col-span-11 text-sm mt-[2px] text-center justify-center">
                Continue with Google
              </p>
            </div>
            <div className="border border-gray-400 rounded-md p-3 w-full grid grid-cols-12 cursor-pointer hover:bg-gray-200 ransition-all duration-300 ease-in-out transform hover:scale-[101%] hover:shadow-md">
              <Image
                src={AuthAsset.facebook}
                alt="facebook"
                width={24}
                height={24}
                className="col-span-1"
              />
              <p className="col-span-11 text-sm mt-[2px] text-center justify-center">
                Continue with Facebook
              </p>
            </div>
          </div>

          <div className="w-full flex items-center gap-4 my-6">
            <div className="h-[1px] flex-1 bg-gray-300"></div>
            <div className="text-sm text-gray-500">OR</div>
            <div className="h-[1px] flex-1 bg-gray-300"></div>
          </div>

          <form onSubmit={handleSubmit} className="w-full">
            <div className="flex flex-col gap-[16px]">
              <div className="w-full flex flex-col gap-[8px] relative">
                <div className="w-full flex flex-col gap-[2px]">
                  <Input
                    type="text"
                    value={email}
                    onChange={handleEmailChange}
                    placeholder="Enter your email"
                    className={`w-full rounded-md text-[12px] text-[#667085] leading-[15.12px] font-[500] h-[48px] border ${
                      errors.email ? "border-[#F81404]" : "border-[#e2b4ac]"
                    } outline-none py-[13px] pl-[13px]`}
                  />
                  {errors.email && (
                    <small className="text-[12px] text-[#F81404]">
                      {errors.email}
                    </small>
                  )}
                </div>
              </div>
            </div>
            <div className="mt-[0px] flex justify-between mb-[15px]"></div>

            <div className="flex flex-col gap-[24px]">
              <div className="flex flex-col gap-[16px]">
                <Button
                  type="submit"
                  variant="default"
                  className="py-6 bg-[#d03417] text-white rounded-md cursor-pointer"
                  //   disabled={googleloading ? true : false}
                >
                  <span>Continue</span>
                </Button>
              </div>

              <div className="flex justify-center items-center">
                <p className="text-xs font-[400] leading-[21px]">
                  By clicking Continue, you accept our{" "}
                  <Link href="/terms">
                    <span className="text-[14px] font-[500] leading-[21px] text-[#d03417] hover:text-[#0A0A0A]">
                      Terms of Service
                    </span>
                  </Link>{" "}
                  and{" "}
                  <Link href="/privacy">
                    <span className="text-[14px] font-[500] leading-[21px] text-[#d03417] hover:text-[#0A0A0A]">
                      Privacy Policy
                    </span>
                  </Link>
                </p>
              </div>
            </div>
          </form>
        </section>
      </main>
    </>
  );
}

export default Auth;
