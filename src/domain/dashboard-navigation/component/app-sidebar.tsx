"use client";

import * as React from "react";
import {
  LayoutDashboard,
  HelpCircle,
  ShoppingCart,
  FileQuestion,
  Receipt,
  Grid2X2,
  Store,
  Truck,
  Package,
  Inbox,
  BarChart,
  Heart,
  Clock,
  Users,
  CreditCard,
  Bell,
  Wallet,
  Map,
  AlertCircle,
  UserCog,
  History,
  Box,
  Percent,
  TrendingUp,
} from "lucide-react";

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { NavMain } from "./nav-main";
import { NavUser } from "./nav-user";
import { RoleSwitcher } from "./role-switcher";
import { NavSecondary } from "./nav-secondary";
import { useRouter } from "next/navigation";

const navigationData = {
  Buyer: {
    main: [
      {
        title: "Dashboard",
        label: "Overview Dashboard",
        url: "/dashboard",
        icon: LayoutDashboard,
      },
      {
        title: "Orders",
        label: "View Your Orders",
        url: "/dashboard/buyer/orders",
        icon: ShoppingCart,
      },
      {
        title: "Requests",
        label: "Manage Requests",
        url: "/dashboard/buyer/requests",
        icon: FileQuestion,
      },
    ],
    shopping: [
      {
        title: "Wishlist",
        label: "Saved Items",
        url: "/dashboard/buyer/wishlist",
        icon: Heart,
      },
      {
        title: "Recent",
        label: "View History",
        url: "/dashboard/buyer/recent",
        icon: Clock,
      },
      {
        title: "Saved-Sellers",
        label: "Favorite Sellers",
        url: "/dashboard/buyer/saved-sellers",
        icon: Users,
      },
    ],
    communication: [
      {
        title: "Inbox",
        label: "Messages",
        url: "/dashboard/buyer/inbox",
        icon: Inbox,
      },
      {
        title: "alerts",
        label: "Updates & Alerts",
        url: "/dashboard/buyer/alerts",
        icon: Bell,
      },
    ],
    finance: [
      {
        title: "Transactions",
        label: "Payment Records",
        url: "/dashboard/buyer/transactions",
        icon: History,
      },
    ],
    analytics: [
      {
        title: "Reports",
        label: "View Analytics",
        url: "/dashboard/buyer/reports",
        icon: BarChart,
      },
    ],
  },
  Seller: {
    main: [
      {
        title: "Dashboard",
        label: "Seller Overview",
        url: "/dashboard",
        icon: LayoutDashboard,
      },
      {
        title: "Store",
        label: "My Store",
        url: "/dashboard/seller/store",
        icon: Store,
      },
      {
        title: "Products",
        label: "Manage Products",
        url: "/dashboard/seller/products",
        icon: Box,
      },
    ],
    sales: [
      {
        title: "Orders",
        label: "Manage Orders",
        url: "/dashboard/seller/orders",
        icon: ShoppingCart,
      },
      {
        title: "Promotions",
        label: "Special Offers",
        url: "/dashboard/seller/promotions",
        icon: Percent,
      },
    ],
    customers: [
      {
        title: "Customers",
        label: "Customer List",
        url: "/dashboard/seller/customers",
        icon: Package,
      },
    ],
    communication: [
      {
        title: "Inbox",
        label: "Messages",
        url: "/dashboard/seller/inbox",
        icon: Inbox,
      },
      {
        title: "alerts",
        label: "Updates & Alerts",
        url: "/dashboard/seller/alerts",
        icon: Bell,
      },
    ],
    finance: [
      {
        title: "Earnings",
        label: "Revenue Details",
        url: "/dashboard/seller/earnings",
        icon: Wallet,
      },
    ],
    analytics: [
      {
        title: "Reports",
        label: "Sales Analytics",
        url: "/dashboard/seller/reports",
        icon: BarChart,
      },
    ],
  },
  Rider: {
    main: [
      {
        title: "Dashboard",
        label: "Rider Overview",
        url: "/dashboard",
        icon: LayoutDashboard,
      },
      {
        title: "Deliveries",
        label: "Current Orders",
        url: "/dashboard/rider/deliveries",
        icon: Truck,
      },
      {
        title: "History",
        label: "Past Deliveries",
        url: "/dashboard/rider/history",
        icon: History,
      },
    ],
    finance: [
      {
        title: "Earnings",
        label: "Income Details",
        url: "/dashboard/rider/earnings",
        icon: Receipt,
      },
      {
        title: "Payments",
        label: "Transaction Log",
        url: "/dashboard/rider/payments",
        icon: CreditCard,
      },
    ],
    navigation: [
      {
        title: "Map",
        label: "Delivery Routes",
        url: "/dashboard/rider/map",
        icon: Map,
      },
      {
        title: "Zones",
        label: "Service Areas",
        url: "/dashboard/rider/zones",
        icon: Grid2X2,
      },
    ],
    performance: [
      {
        title: "Reports",
        label: "Performance Data",
        url: "/dashboard/rider/reports",
        icon: BarChart,
      },
      {
        title: "Stats",
        label: "Metrics Overview",
        url: "/dashboard/rider/stats",
        icon: TrendingUp,
      },
    ],
    communication: [
      {
        title: "Inbox",
        label: "Messages",
        url: "/dashboard/rider/inbox",
        icon: Inbox,
      },
      {
        title: "alerts",
        label: "Updates & Alerts",
        url: "/dashboard/rider/alerts",
        icon: Bell,
      },
      {
        title: "Support",
        label: "Help Center",
        url: "/dashboard/rider/support",
        icon: AlertCircle,
      },
    ],
  },
};

const data = {
  user: {
    name: "Divine Ikechukwu",
    email: "<EMAIL>",
    avatar: "/avatars/shadcn.jpg",
  },
  navSecondary: [
    {
      title: "Settings",
      label: "Manage Your Account",
      url: "/dashboard/settings",
      icon: UserCog,
    },
    {
      title: "Help",
      label: "Get Support",
      url: "/dashboard/help",
      icon: HelpCircle,
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const [currentRole, setCurrentRole] =
    React.useState<keyof typeof navigationData>("Buyer");
  const router = useRouter();

  const handleRoleChange = (newRole: mode) => {
    setCurrentRole(newRole);
    router.push("/dashboard");
  };

  const getCurrentRoleNavigation = () => {
    return navigationData[currentRole];
  };

  console.log("Current getCurrentRoleNavigation:", getCurrentRoleNavigation());

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader className="group-data-[state=collapsed]:px-2  py-2 p group-data-[state=collapsed]:py-5 x-4">
        <SidebarHeader className="px-0">
          <NavUser user={data.user} />
        </SidebarHeader>
      </SidebarHeader>
      <SidebarMenu>
        <SidebarMenuItem className="group-data-[state=collapsed]:px-2 px-2">
          <RoleSwitcher
            currentRole={currentRole}
            onRoleChange={handleRoleChange}
          />
        </SidebarMenuItem>
      </SidebarMenu>
      <SidebarContent>
        <NavMain items={getCurrentRoleNavigation()} />
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>{/**/}</SidebarFooter>
    </Sidebar>
  );
}
