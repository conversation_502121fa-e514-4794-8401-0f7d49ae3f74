"use client";

import { LucideIcon } from "lucide-react";
import { usePathname } from "next/navigation";
import Link from "next/link";

import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";

export function NavMain({
  items,
}: {
  items: {
    [key: string]: {
      title: string;
      label: string;
      url: string;
      icon?: LucideIcon;
    }[];
  };
}) {
  const pathname = usePathname();

  return (
    <div className="flex flex-col ">
      {Object.entries(items).map(([section, sectionItems]) => (
        <SidebarGroup
          key={section}
          className="group-data-[state=collapsed]:p-2 py-2 px-4"
        >
          <SidebarGroupLabel className="text-muted-foreground capitalize">
            {section}
          </SidebarGroupLabel>
          <SidebarGroupContent className="flex flex-col gap-2">
            <SidebarMenu>
              {sectionItems.map(item => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    tooltip={item.title}
                    className={
                      item.title.toLowerCase() === "dashboard"
                        ? pathname === "/dashboard"
                          ? "bg-[#cc3a1b] text-white hover:text-white/80 hover:bg-[#cc3a1b]/90 transition-colors duration-200"
                          : "hover:bg-[#cc3a1b]/20 transition-colors duration-200 hover:text-black/80"
                        : pathname
                              .toLowerCase()
                              .includes(item.title.toLowerCase())
                          ? "bg-[#cc3a1b] text-white hover:text-white/80 hover:bg-[#cc3a1b]/90 transition-colors duration-200"
                          : "hover:bg-[#cc3a1b]/20 transition-colors duration-200 hover:text-black/80"
                    }
                  >
                    <Link href={item.url}>
                      {item.icon && <item.icon />}
                      <span>{item.label}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      ))}
    </div>
  );
}
