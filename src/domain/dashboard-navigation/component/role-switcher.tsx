"use client";

import * as React from "react";
import { ChevronsUpDown, ShoppingBag, Bike, Wallet } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { SidebarMenuButton } from "@/components/ui/sidebar";

interface RoleSwitcherProps {
  currentRole: mode;
  onRoleChange: (role: mode) => void;
}

export function RoleSwitcher({ currentRole, onRoleChange }: RoleSwitcherProps) {
  const roles = [
    {
      id: "Seller",
      icon: ShoppingBag,
      label: "Seller Mode",
      description: "Manage your store and products",
    },
    {
      id: "Buyer",
      icon: Wallet,
      label: "Buyer Mode",
      description: "Browse and purchase items",
    },
    {
      id: "Rider",
      icon: Bike,
      label: "Rider Mode",
      description: "Manage deliveries",
    },
  ];

  const handleRoleSwitch = (role: mode) => {
    onRoleChange(role);
  };

  const currentRoleData = roles.find(r => r.id === currentRole);
  const Icon = currentRoleData?.icon;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <SidebarMenuButton
          size="lg"
          className="group-data-[state=collapsed]:px-2 py-3 my-2 bg-white data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground hover:bg-sidebar-accent/80 transition-colors border "
        >
          <div className="flex items-center gap-3 group-data-[state=collapsed]:px-1">
            {Icon && <Icon className="size-5 text-primary " />}
            <div className="grid flex-1 text-left text-sm leading-tight group-data-[state=collapsed]:hidden">
              <span className="truncate text-xs text-muted-foreground ">
                Current Role
              </span>
              <span className="truncate font-medium "> {currentRole}</span>
            </div>
          </div>
          <ChevronsUpDown className="ml-auto opacity-50" />
        </SidebarMenuButton>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-72 p-2 mx-2" align="end">
        <DropdownMenuLabel className="text-xs text-muted-foreground px-2 py-1.5">
          Select Your Role
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        {roles.map(role => (
          <DropdownMenuItem
            key={role.id}
            onClick={() => handleRoleSwitch(role.id as mode)}
            className="px-4 py-3 cursor-pointer"
          >
            <div className="flex items-center gap-3">
              <role.icon className="size-5 text-primary" />
              <div className="grid gap-0.5">
                <span className="font-medium">{role.label}</span>
                <span className="text-xs text-muted-foreground">
                  {role.description}
                </span>
              </div>
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
