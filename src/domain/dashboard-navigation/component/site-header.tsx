"use client";

import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Input } from "../../../components/ui/input";
import { ChevronRight, Search } from "lucide-react";
import { usePathname } from "next/navigation";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList } from "@/components/ui/breadcrumb";

export function SiteHeader() {
  const pathname = usePathname();
  

  return (
    <header className="flex h-[85px] shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-[85px]">
      <div className="flex w-full items-center gap-1 px-4 lg:gap-2 lg:px-6">
        <SidebarTrigger className="-ml-1" />
        <Separator
          orientation="vertical"
          className="mx-2 data-[orientation=vertical]:h-4"
        />
        <Breadcrumb>
          <BreadcrumbList className="!gap-1">
            {pathname === "/dashboard" ? (
              <BreadcrumbItem className="capitalize text-black" >
                <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
              </BreadcrumbItem>
            ) : (
              pathname.split("/").map((part, index, array) => {
                if (part === "" || ["seller", "buyer", "rider", "dashboard"].includes(part)) return null;
                return (
                  <BreadcrumbItem key={index} className="capitalize text-black">
                    <BreadcrumbLink href={index === array.length - 1 ? pathname : pathname.split('/').slice(0, array.indexOf(part) + 1).join('/')}>
                      {part.replace(/-/g, " ")}
                    </BreadcrumbLink>
                    {index < array.length - 1 && <ChevronRight className="h-4 w-4" /> }
                  </BreadcrumbItem>
                );
              })
            )}
          </BreadcrumbList>
        </Breadcrumb>
        <div className="ml-auto flex items-center gap-2">
          <div className="relative w-84 hidden md:block">
            <Input
              type="search"
              placeholder="Search..."
              className="pl-8 rounded-full bg-muted/30"
            />
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          </div>
        </div>
      </div>
    </header>
  );
}
