import type { JSX } from "react";
import Link from "next/link";

export default function BecomeSeller(): JSX.Element {
  return (
    <section className="bg-[#fff8f6] py-24 px-4 w-screen left-[calc(-50vw+50%)] right-[calc(-50vw+50%)] relative">
      <div className="max-w-7xl mx-auto flex flex-col md:flex-row justify-between items-center gap-6">
        {/* Text Block */}
        <div className="text-center md:text-left">
          <h3 className="text-xl font-semibold mb-1">Become a Seller</h3>
          <p className="text-gray-700">
            We only work with the best stores around Ile-Ife.
          </p>
        </div>

        {/* Button */}
        <Link
          href="/auth/signup"
          className="bg-[#e9cc77] cursor-pointer text-black font-semibold px-8 py-3 rounded-lg hover:bg-[#f2d883] transition"
        >
          REGISTER NOW
        </Link>
      </div>
    </section>
  );
}
