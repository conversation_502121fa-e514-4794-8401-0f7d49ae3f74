import { <PERSON><PERSON> } from "@/components/ui/button";
import { ShoppingBag, Truck, Store, ArrowRight } from "lucide-react";

export default function CoreFeatures() {
  return (
    <section
      className="w-screen left-[calc(-50vw+50%)] right-[calc(-50vw+50%)] relative py-32 px-4 bg-gradient-to-b from-[#cc3a1b]/5 to-transparent"
      id="core-features"
    >
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <span className="text-[#cc3a1b] font-semibold mb-4 block">
            Revolutionary Platform
          </span>
          <h2 className="text-5xl font-bold mb-6 bg-clip-text ">
            Empowering Commerce for Everyone
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Join our innovative ecosystem where sellers thrive with zero fees,
            buyers discover amazing deals, and delivery partners grow their
            business. Welcome to the future of local commerce.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-10">
          <div className="group bg-background/80 rounded-2xl p-10 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-border/50">
            <div className="bg-[#cc3a1b]/10 rounded-full p-4 w-fit mb-6">
              <Store className="w-8 h-8 text-[#cc3a1b]" />
            </div>
            <h3 className="text-2xl font-bold mb-6 group-hover:text-[#cc3a1b] transition-colors">
              Sellers Hub
            </h3>
            <ul className="space-y-5 mb-10">
              <li className="flex items-center gap-3 text-lg text-foreground/90">
                <span className="text-[#cc3a1b] font-bold">✦</span>
                Zero commission forever
              </li>
              <li className="flex items-center gap-3 text-lg text-foreground/90">
                <span className="text-[#cc3a1b] font-bold">✦</span>
                Advanced analytics & insights
              </li>
              <li className="flex items-center gap-3 text-lg text-foreground/90">
                <span className="text-[#cc3a1b] font-bold">✦</span>
                Integrated marketing tools
              </li>
            </ul>
            <Button className="w-full !py-5 bg-[#cc3a1b] text-white font-semibold px-8 py-4 rounded-xl hover:bg-[#cc3a1b]/90 transition-all flex items-center justify-center gap-2 group-hover:gap-4">
              Start Selling <ArrowRight className="w-5 h-5" />
            </Button>
          </div>

          <div className="group bg-[#cc3a1b] text-white rounded-2xl p-10 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
            <div className="bg-background/10 rounded-full p-4 w-fit mb-6">
              <ShoppingBag className="w-8 h-8" />
            </div>
            <h3 className="text-2xl font-bold mb-6">Smart Shopping</h3>
            <ul className="space-y-5 mb-10">
              <li className="flex items-center gap-3 text-lg text-white/90">
                <span className="font-bold">✦</span>
                Exclusive local deals
              </li>
              <li className="flex items-center gap-3 text-lg text-white/90">
                <span className="font-bold">✦</span>
                Verified authentic sellers
              </li>
              <li className="flex items-center gap-3 text-lg text-white/90">
                <span className="font-bold">✦</span>
                Secure payment system
              </li>
            </ul>
            <Button className="w-full !py-5 bg-background text-[#cc3a1b] font-semibold px-8 py-4 rounded-xl hover:bg-background/90 transition-all flex items-center justify-center gap-2 group-hover:gap-4">
              Explore Marketplace <ArrowRight className="w-5 h-5" />
            </Button>
          </div>

          <div className="group bg-background/80 rounded-2xl p-10 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-border/50">
            <div className="bg-[#cc3a1b]/10 rounded-full p-4 w-fit mb-6">
              <Truck className="w-8 h-8 text-[#cc3a1b]" />
            </div>
            <h3 className="text-2xl font-bold mb-6 group-hover:text-[#cc3a1b] transition-colors">
              Delivery Network
            </h3>
            <ul className="space-y-5 mb-10">
              <li className="flex items-center gap-3 text-lg text-foreground/90">
                <span className="text-[#cc3a1b] font-bold">✦</span>
                Flexible working hours
              </li>
              <li className="flex items-center gap-3 text-lg text-foreground/90">
                <span className="text-[#cc3a1b] font-bold">✦</span>
                Competitive compensation
              </li>
              <li className="flex items-center gap-3 text-lg text-foreground/90">
                <span className="text-[#cc3a1b] font-bold">✦</span>
                Smart route optimization
              </li>
            </ul>
            <Button className="w-full !py-5 bg-[#cc3a1b] text-white font-semibold px-8 py-4 rounded-xl hover:bg-[#cc3a1b]/90 transition-all flex items-center justify-center gap-2 group-hover:gap-4">
              Join Network <ArrowRight className="w-5 h-5" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
