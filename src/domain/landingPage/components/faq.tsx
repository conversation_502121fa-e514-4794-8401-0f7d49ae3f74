import type { JSX } from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

type FaqItem = {
  question: string;
  answer: string;
};

const faqData: FaqItem[] = [
  {
    question: "What payment methods are accepted on Markket?",
    answer:
      "Markket supports various payment methods including bank transfers, card payments, and mobile money. All transactions are secure and processed through our trusted payment partners to ensure safety for both buyers and sellers.",
  },
  {
    question: "How do I become a seller on Markket?",
    answer:
      "Becoming a seller is simple and free! Just sign up for a Markket account, complete your seller profile, and start listing your products. We have zero platform fees, meaning you keep 100% of your sales revenue.",
  },
  {
    question: "How does delivery work?",
    answer:
      "We partner with local delivery services to ensure quick and reliable delivery. Sellers can choose to handle their own delivery or use our network of verified delivery partners. Delivery fees are transparent and calculated based on distance and package size.",
  },
  {
    question: "What happens if I'm not satisfied with my purchase?",
    answer:
      "We have a buyer protection policy in place. If you receive an item that's significantly different from what was described, you can initiate a return within 48 hours of delivery. Our support team will help resolve any issues between buyers and sellers.",
  },
  {
    question: "Is Markket available in my location?",
    answer:
      "Currently, Markket is available in major cities across Nigeria, with plans to expand to more locations. You can check if we're operating in your area by entering your location on our homepage or during registration.",
  },
];

export default function FaqSection(): JSX.Element {
  return (
    <section className="bg-gray-100 text-black py-24 px-4 w-screen left-[calc(-50vw+50%)] right-[calc(-50vw+50%)] relative">
      <div className="max-w-4xl mx-auto text-center mb-12">
        <h2 className="text-3xl font-bold mb-2">
          Have Questions About Markket?
        </h2>
        <p className="text-gray-600">
          Find answers to commonly asked questions about our marketplace
          platform
        </p>
      </div>

      <div className="space-y-4 max-w-3xl mx-auto">
        <Accordion type="single" collapsible>
          {faqData.map((item, index) => (
            <AccordionItem key={index} value={`item-${index}`}>
              <AccordionTrigger className="text-left text-lg font-medium py-4 px-6">
                {item.question}
              </AccordionTrigger>
              <AccordionContent className="px-6">
                <p className="text-sm py-4 text-black/80 leading-relaxed">
                  {item.answer}
                </p>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </section>
  );
}
