import { ArrowR<PERSON> } from "lucide-react";
import React from "react";

import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";
import { asset } from "public/images";
import { Badge } from "@/components/ui/badge";

interface HeroProps {
  heading?: string;
  description?: string;
  ctaButton?: {
    text: string;
    url: string;
  };
}

const Hero = ({
  heading = "Discover a smarter way to shop, sell, and deliver.",
  description = "Shop from local stores, sell your products, or join our delivery network - all with zero platform fees. Experience the future of local commerce.",
  ctaButton = {
    text: "Explore Marketplace",
    url: "/shop",
  },
}: HeroProps) => {
  return (
    <section className="relative py-48 px-3 md:px-0 flex items-center justify-center">
      <div className="absolute inset-0 w-full h-full">
        <Image
          src={asset.heroImage}
          alt="Hero background"
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-b from-black/20 to-black/80" />
      </div>

      <div className="container text-center py-16 relative z-10">
        <div className="mx-auto flex max-w-5xl items-center flex-col gap-4">
          <Badge
            variant="outline"
            className="text-white py-1 px-2 rounded-full shadow-lg"
          >
            {"CONNECT, SHOP, AND DELIVER WITH ZERO CHARGES"}
          </Badge>

          <h1 className="text-4xl font-extrabold lg:text-7xl text-white">
            {heading}
          </h1>

          <p className="text-white/80 text-balance text-xl max-w-3xl mx-auto">
            {description}
          </p>
        </div>

        <div className="mt-12 flex justify-center gap-6">
          <Button
            size="lg"
            className="gap-2 bg-[#cc3a1b] hover:bg-[#cc3a1b]/80"
            asChild
          >
            <a href={ctaButton.url}>
              {ctaButton.text}
              <ArrowRight className="size-4" />
            </a>
          </Button>
        </div>
      </div>
    </section>
  );
};

export default Hero;
