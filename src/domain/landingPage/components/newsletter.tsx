import { Send } from "lucide-react";
import { type JSX } from "react";

export default function NewsletterSignup(): JSX.Element {
  async function handleSubmit(formData: FormData) {
    "use server";
    const email = formData.get("email");
    // Send email to newsletter backend or API
    console.log("Subscribed:", email);
  }

  return (
    <section className="bg-[#204D45] text-white py-24 px-4 w-screen left-[calc(-50vw+50%)] right-[calc(-50vw+50%)] relative">
      <div className="max-w-3xl mx-auto text-center">
        {/* Icon */}
        <div className="mx-auto w-12 h-12 rounded-full bg-[#e9cc77] flex items-center justify-center mb-6">
          <Send size={20} className="text-[#204D45]" />{" "}
        </div>

        {/* Title + Subtitle */}
        <h2 className="text-2xl md:text-3xl font-semibold mb-2">
          Stay Up to Date
        </h2>
        <p className="text-white/80 mb-8">
          Subscribe to our newsletter to receive our weekly feed.
        </p>

        {/* Form */}
        <form
          action={handleSubmit}
          className="flex items-center bg-white/10 rounded-lg overflow-hidden max-w-xl mx-auto"
        >
          <input
            type="email"
            name="email"
            required
            placeholder="Your e-mail"
            className="flex-grow px-6 py-3 bg-transparent text-white placeholder-white/60 outline-none"
          />
          <button
            type="submit"
            className="flex items-center gap-2 bg-white/10 hover:bg-white/20 text-white font-semibold px-6 py-3 transition"
          >
            SEND <Send size={16} />
          </button>
        </form>
      </div>
    </section>
  );
}
