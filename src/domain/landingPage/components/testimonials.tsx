"use client";

import Image from "next/image";
import { Avatar, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";
import AutoScroll from "embla-carousel-auto-scroll";

const Testimonials = () => {
  const testimonials = [
    {
      quote:
        "Markket has transformed how I sell my traditional adire fabrics. The platform connects me directly with customers from all over Nigeria.",
      name: "<PERSON><PERSON><PERSON><PERSON>",
      position: "Owner, Adire Heritage Store",
      avatar: "https://images.unsplash.com/photo-1531123897727-8f129e1688ce",
    },
    {
      quote:
        "As a student at OAU, finding quality local products was difficult until I discovered Markket. Now I can easily shop from trusted vendors in Ile-Ife.",
      name: "<PERSON><PERSON>waseu<PERSON> Ogunleye",
      position: "Student, Obafemi Awolowo University",
      avatar: "https://images.unsplash.com/photo-1507152832244-10d45c7eda57",
    },
    {
      quote:
        "My restaurant business has grown significantly since joining Markket. The delivery network makes it easy to reach customers across Ile-Ife.",
      name: "<PERSON><PERSON><PERSON><PERSON>",
      position: "Owner, <PERSON>'s Kitchen",
      avatar: "https://images.unsplash.com/photo-1523824921871-d6f1a15151f1",
    },
    {
      quote:
        "I love how Markket promotes local businesses. As a market trader at Oja Ife, the platform has helped me reach more customers digitally.",
      name: "Biodun Oyedepo",
      position: "Trader, Oja Ife Market",
      avatar: "https://images.unsplash.com/photo-1544005313-94ddf0286df2",
    },
    {
      quote:
        "The zero-fee policy on Markket has helped me grow my electronics business. I can offer better prices to my customers in Ile-Ife.",
      name: "Tunde Adeyemi",
      position: "Owner, TechHub Electronics",
      avatar: "https://images.unsplash.com/photo-1506277886164-e25aa3f4ef7f",
    },
    {
      quote:
        "As a delivery partner, Markket's platform is user-friendly and helps me earn a good income serving the Ile-Ife community.",
      name: "Kayode Olatunji",
      position: "Independent Delivery Partner",
      avatar: "https://images.unsplash.com/photo-1547425260-76bcadfb4f2c",
    },
    {
      quote:
        "The local focus of Markket makes it perfect for my handmade jewelry business. I've connected with many customers around OAU campus.",
      name: "Aisha Mohammed",
      position: "Artisan Jeweler",
      avatar: "https://images.unsplash.com/photo-1494790108377-be9c29b29330",
    },
    {
      quote:
        "Markket has revolutionized how we do business in Ile-Ife. The platform brings buyers and sellers together seamlessly.",
      name: "Babajide Ogunlesi",
      position: "Local Business Association Leader",
      avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e",
    },
  ];

  return (
    <section className="py-32 px-3 md:px-0 flex items-center justify-center bg-[#CB3D1F]">
      <div className="container max-w-7xl">
        <div className="flex flex-col gap-6">
          <div className="grid grid-cols-1 items-stretch gap-x-0 gap-y-4 lg:grid-cols-3 lg:gap-4">
            <div className="h-72 w-full lg:h-auto max-h-[260px] relative">
              <Image
                src="https://images.unsplash.com/photo-1506277886164-e25aa3f4ef7f"
                alt="Ile-Ife marketplace"
                fill
                className="rounded-md object-cover"
              />
            </div>
            <Card className="col-span-2 flex items-center justify-center p-6">
              <div className="flex flex-col gap-4">
                <q className="text-xl font-medium lg:text-3xl">
                  Markket has brought new life to our local commerce in Ile-Ife.
                  It&apos;s not just a platform, it&apos;s a community that
                  supports and empowers local businesses to thrive in the
                  digital age.
                </q>
                <div className="flex flex-col items-start">
                  <p>Chief Adebayo Ogunlesi</p>
                  <p className="text-muted-foreground">Local Business Owner</p>
                </div>
              </div>
            </Card>
          </div>
          <div className="relative mx-auto flex w-full items-center justify-center">
            <Carousel
              opts={{
                loop: true,
                align: "start",
              }}
              plugins={[
                AutoScroll({
                  playOnInit: true,
                  speed: 1,
                  stopOnInteraction: false,
                  stopOnMouseEnter: true,
                }),
              ]}
              className="overflow-hidden"
            >
              <CarouselContent className="ml-0">
                {testimonials.map((testimonial, index) => (
                  <CarouselItem
                    key={index}
                    className="flex basis-full pl-0 md:basis-1/2 lg:basis-1/3"
                  >
                    <div className="mx-2">
                      <Card className="w-full h-full">
                        <CardContent className="px-6 pt-6 leading-7 text-foreground/70">
                          <q>{testimonial.quote}</q>
                        </CardContent>
                        <CardFooter>
                          <div className="flex gap-4 leading-5">
                            <Avatar className="size-9 rounded-full ring-1 ring-input object-cover">
                              <AvatarImage
                                src={testimonial.avatar}
                                alt={testimonial.name}
                                className="object-cover"
                              />
                            </Avatar>
                            <div className="text-sm">
                              <p className="font-medium">{testimonial.name}</p>
                              <p className="text-muted-foreground">
                                {testimonial.position}
                              </p>
                            </div>
                          </div>
                        </CardFooter>
                      </Card>
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>
            </Carousel>
            <div className="absolute inset-y-0 left-0 w-12 bg-gradient-to-r from-[#CB3D1F] to-transparent"></div>
            <div className="absolute inset-y-0 right-0 w-12 bg-gradient-to-l from-[#CB3D1F] to-transparent"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
