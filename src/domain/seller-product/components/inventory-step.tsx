// const InventoryStep: React.FC<{
//   data: StoreData["inventory"];
//   onUpdate: (data: Partial<StoreData["inventory"]>) => void;
//   onNext: () => void;
//   onPrev: () => void;
// }> = ({ data, onUpdate, onNext, onPrev }) => {
//   return (
//     <div className="max-w-2xl mx-auto">
//       <div className="bg-white rounded-lg shadow-sm p-8">
//         <h2 className="text-2xl font-bold text-gray-900 mb-6">
//           Inventory Management
//         </h2>
//         <p className="text-gray-600 mb-6">
//           Set up inventory tracking for your products to manage stock levels
//           effectively.
//         </p>

//         <div className="space-y-6">
//           <div>
//             <label className="flex items-center space-x-3">
//               <input
//                 type="checkbox"
//                 checked={data.trackInventory}
//                 onChange={e => onUpdate({ trackInventory: e.target.checked })}
//                 className="w-4 h-4 text-black"
//               />
//               <span className="text-sm font-medium text-gray-700">
//                 Track inventory quantities
//               </span>
//             </label>
//             <p className="text-sm text-gray-500 mt-1 ml-7">
//               Enable this to track stock levels and get low inventory alerts
//             </p>
//           </div>

//           {data.trackInventory && (
//             <>
//               <div>
//                 <label className="block text-sm font-medium text-gray-700 mb-2">
//                   Current Stock Quantity
//                 </label>
//                 <input
//                   type="number"
//                   value={data.stockQuantity}
//                   onChange={e =>
//                     onUpdate({ stockQuantity: parseInt(e.target.value) || 0 })
//                   }
//                   className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
//                   placeholder="Enter quantity"
//                 />
//               </div>
//               <div>
//                 <label className="block text-sm font-medium text-gray-700 mb-2">
//                   Low Stock Alert Threshold
//                 </label>
//                 <input
//                   type="number"
//                   value={data.lowStockAlert}
//                   onChange={e =>
//                     onUpdate({ lowStockAlert: parseInt(e.target.value) || 0 })
//                   }
//                   className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
//                   placeholder="Trigger alert when stock reaches..."
//                 />
//               </div>
//             </>
//           )}
//         </div>
//         <div className="flex justify-between mt-8">
//           <button
//             onClick={onPrev}
//             className="flex items-center px-6 py-2 text-gray-600 hover:text-gray-800"
//           >
//             <ChevronLeft className="w-5 h-5 mr-2" />
//             Previous
//           </button>
//           <button
//             onClick={onNext}
//             className="bg-black hover:bg-black/80 cursor-pointer text-white px-6 py-2 rounded-lg flex items-center"
//           >
//             Continue
//             <ChevronRight className="w-5 h-5 ml-2" />
//           </button>
//         </div>
//       </div>
//     </div>
//   );
// };
