
import React, { useState } from "react";
import { ChevronRight, ChevronLeft, Upload } from "lucide-react";
import { StoreData } from "@/domain/seller-store/types";

export const BusinessDetailsStep: React.FC<{
    data: StoreData["businessDetails"];
    onUpdate: (data: Partial<StoreData["businessDetails"]>) => void;
    onNext: () => void;
    onPrev: () => void;
  }> = ({ data, onUpdate, onNext, onPrev }) => {
    const [errors, setErrors] = useState<Record<string, string>>({});
  
    const validate = () => {
      const newErrors: Record<string, string> = {};
      if (!data.legalName) newErrors.legalName = "Legal name is required";
      if (!data.address) newErrors.address = "Address is required";
      if (!data.phone) newErrors.phone = "Phone number is required";
      if (!data.email) newErrors.email = "Email is required";
  
      setErrors(newErrors);
      return Object.keys(newErrors).length === 0;
    };
  
    const handleNext = () => {
      if (validate()) {
        onNext();
      }
    };
  
    return (
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Business Details & Verification
          </h2>
  
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Legal Business Name *
              </label>
              <input
                type="text"
                value={data.legalName}
                onChange={e => onUpdate({ legalName: e.target.value })}
                className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.legalName ? "border-red-500" : "border-gray-300"
                }`}
                placeholder="Official registered business name"
              />
              {errors.legalName && (
                <p className="text-red-500 text-sm mt-1">{errors.legalName}</p>
              )}
            </div>
  
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Business Registration Number
                </label>
                <input
                  type="text"
                  value={data.registrationNumber}
                  onChange={e => onUpdate({ registrationNumber: e.target.value })}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Registration number"
                />
              </div>
  
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tax ID / VAT Number
                </label>
                <input
                  type="text"
                  value={data.taxId}
                  onChange={e => onUpdate({ taxId: e.target.value })}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Tax identification number"
                />
              </div>
            </div>
  
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Business Address *
              </label>
              <textarea
                value={data.address}
                onChange={e => onUpdate({ address: e.target.value })}
                rows={3}
                className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.address ? "border-red-500" : "border-gray-300"
                }`}
                placeholder="Full business address including city, state, and postal code"
              />
              {errors.address && (
                <p className="text-red-500 text-sm mt-1">{errors.address}</p>
              )}
            </div>
  
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number *
                </label>
                <input
                  type="tel"
                  value={data.phone}
                  onChange={e => onUpdate({ phone: e.target.value })}
                  className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.phone ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="+****************"
                />
                {errors.phone && (
                  <p className="text-red-500 text-sm mt-1">{errors.phone}</p>
                )}
              </div>
  
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Contact Email *
                </label>
                <input
                  type="email"
                  value={data.email}
                  onChange={e => onUpdate({ email: e.target.value })}
                  className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.email ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="<EMAIL>"
                />
                {errors.email && (
                  <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                )}
              </div>
            </div>
  
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Verification Documents
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Business License
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                    <Upload className="w-6 h-6 text-gray-400 mx-auto mb-1" />
                    <p className="text-xs text-gray-600">Upload document</p>
                    <input
                      type="file"
                      accept=".pdf,.jpg,.png"
                      className="hidden"
                    />
                  </div>
                </div>
  
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tax Certificate
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                    <Upload className="w-6 h-6 text-gray-400 mx-auto mb-1" />
                    <p className="text-xs text-gray-600">Upload document</p>
                    <input
                      type="file"
                      accept=".pdf,.jpg,.png"
                      className="hidden"
                    />
                  </div>
                </div>
  
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    ID Verification
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                    <Upload className="w-6 h-6 text-gray-400 mx-auto mb-1" />
                    <p className="text-xs text-gray-600">Upload document</p>
                    <input
                      type="file"
                      accept=".pdf,.jpg,.png"
                      className="hidden"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
  
          <div className="flex justify-between mt-8">
            <button
              onClick={onPrev}
              className="flex items-center px-6 py-2 text-gray-600 hover:text-gray-800"
            >
              <ChevronLeft className="w-5 h-5 mr-2" />
              Previous
            </button>
            <button
              onClick={handleNext}
              className="bg-black hover:bg-black/80 cursor-pointer text-white px-6 py-2 rounded-lg flex items-center"
            >
              Continue
              <ChevronRight className="w-5 h-5 ml-2" />
            </button>
          </div>
        </div>
      </div>
    );
  };
  