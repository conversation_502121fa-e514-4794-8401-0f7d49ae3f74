
import React, { useState } from "react";
import { ChevronRight, ChevronLeft } from "lucide-react";
import { StoreData } from "@/domain/seller-store/types";

export const CategoriesStep: React.FC<{
    data: StoreData["categories"];
    onUpdate: (data: Partial<StoreData["categories"]>) => void;
    onNext: () => void;
    onPrev: () => void;
  }> = ({ data, onUpdate, onNext, onPrev }) => {
    const [newCategory, setNewCategory] = useState("");
    const [newCollection, setNewCollection] = useState("");
  
    const addCategory = () => {
      if (newCategory.trim()) {
        onUpdate({
          mainCategories: [...data.mainCategories, newCategory.trim()],
        });
        setNewCategory("");
      }
    };
  
    const removeCategory = (index: number) => {
      onUpdate({
        mainCategories: data.mainCategories.filter((_, i) => i !== index),
      });
    };
  
    const addCollection = () => {
      if (newCollection.trim()) {
        onUpdate({
          collections: [...data.collections, newCollection.trim()],
        });
        setNewCollection("");
      }
    };
  
    const removeCollection = (index: number) => {
      onUpdate({
        collections: data.collections.filter((_, i) => i !== index),
      });
    };
  
    return (
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Store Categories & Organization
          </h2>
  
          <div className="space-y-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Product Categories
              </h3>
              <p className="text-gray-600 mb-4">
                Organize your products into categories to help customers find what
                they&apos;re looking for.
              </p>
  
              <div className="flex space-x-2 mb-4">
                <input
                  type="text"
                  value={newCategory}
                  onChange={e => setNewCategory(e.target.value)}
                  onKeyPress={e => e.key === "Enter" && addCategory()}
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter category name"
                />
                <button
                  onClick={addCategory}
                  className="bg-black hover:bg-black/80 cursor-pointer text-white px-4 py-2 rounded-lg"
                >
                  Add
                </button>
              </div>
  
              <div className="space-y-2">
                {data.mainCategories.map((category, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between bg-gray-50 px-4 py-2 rounded-lg"
                  >
                    <span>{category}</span>
                    <button
                      onClick={() => removeCategory(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      Remove
                    </button>
                  </div>
                ))}
              </div>
            </div>
  
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Collections
              </h3>
              <p className="text-gray-600 mb-4">
                Create collections to group products by themes, seasons, or
                special promotions.
              </p>
  
              <div className="flex space-x-2 mb-4">
                <input
                  type="text"
                  value={newCollection}
                  onChange={e => setNewCollection(e.target.value)}
                  onKeyPress={e => e.key === "Enter" && addCollection()}
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter collection name (e.g., Summer Sale, New Arrivals)"
                />
                <button
                  onClick={addCollection}
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg"
                >
                  Add
                </button>
              </div>
  
              <div className="space-y-2">
                {data.collections.map((collection, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between bg-green-50 px-4 py-2 rounded-lg"
                  >
                    <span>{collection}</span>
                    <button
                      onClick={() => removeCollection(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      Remove
                    </button>
                  </div>
                ))}
              </div>
            </div>
  
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-semibold text-yellow-800 mb-2">
                💡 Organization Tips
              </h4>
              <ul className="text-yellow-700 text-sm space-y-1">
                <li>• Keep category names clear and descriptive</li>
                <li>• Limit to 5-8 main categories for better navigation</li>
                <li>• Use collections for seasonal or promotional groupings</li>
                <li>• You can always add more categories after launching</li>
              </ul>
            </div>
          </div>
  
          <div className="flex justify-between mt-8">
            <button
              onClick={onPrev}
              className="flex items-center px-6 py-2 text-gray-600 hover:text-gray-800"
            >
              <ChevronLeft className="w-5 h-5 mr-2" />
              Previous
            </button>
            <button
              onClick={onNext}
              className="bg-black hover:bg-black/80 cursor-pointer text-white px-6 py-2 rounded-lg flex items-center"
            >
              Continue
              <ChevronRight className="w-5 h-5 ml-2" />
            </button>
          </div>
        </div>
      </div>
    );
  };