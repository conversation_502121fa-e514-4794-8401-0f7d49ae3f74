
import React from "react";
import { ChevronR<PERSON>, Check, Rocket } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { StoreData } from "@/domain/seller-store/types";

export const LaunchStep: React.FC<{
    data: StoreData;
    onPrev: () => void;
  }> = ({ data, onPrev }) => {
    return (
      <div className="text-center py-16">
        <div className="max-w-2xl mx-auto">
          <Rocket className="w-16 h-16 mx-auto text-green-600 mb-8" />
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Your Store is Ready!
          </h2>
          <p className="text-sm text-gray-600 mb-8">
            Congratulations! Your store &quot;{data.basicInfo.storeName}&quot; has been
            successfully created.
          </p>
          <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
            <h3 className="font-semibold text-green-800 mb-2">
              What Happens Next?
            </h3>
            <ul className="text-green-700 text-left space-y-2">
              <li>
                <Check className="w-4 h-4 inline-block mr-2" /> Your store will go
                live shortly
              </li>
              <li>
                <Check className="w-4 h-4 inline-block mr-2" /> Payment gateways
                have been configured
              </li>
              <li>
                <Check className="w-4 h-4 inline-block mr-2" /> Inventory tracking
                is now active
              </li>
              <li>
                <Check className="w-4 h-4 inline-block mr-2" /> You&apos;ll receive a
                confirmation email shortly
              </li>
            </ul>
          </div>
          <Button
            onClick={onPrev}
            className="bg-black hover:bg-black/80 cursor-pointer text-white px-8 py-3 rounded-lg font-semibold flex items-center mx-auto"
          >
            Go to Dashboard
            <ChevronRight className="w-5 h-5 ml-2" />
          </Button>
        </div>
      </div>
    );
  };