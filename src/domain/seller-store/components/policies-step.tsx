
import React from "react";
import { ChevronRight, ChevronLeft } from "lucide-react";
import { StoreData } from "@/domain/seller-store/types";

export const PoliciesStep: React.FC<{
    data: StoreData["policies"];
    onUpdate: (data: Partial<StoreData["policies"]>) => void;
    onNext: () => void;
    onPrev: () => void;
  }> = ({ data, onUpdate, onNext, onPrev }) => {
    const processingTimes = ["1-2 days", "3-5 days", "5-7 days", "1-2 weeks"];
    const returnWindows = ["7 days", "14 days", "30 days", "60 days", "90 days"];
  
    return (
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Store Policies & Settings
          </h2>
  
          <div className="space-y-8">
            {/* Shipping Policies */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Shipping Policies
              </h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Processing Time
                  </label>
                  <select
                    value={data.processingTime}
                    onChange={e => onUpdate({ processingTime: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Select processing time</option>
                    {processingTimes.map(time => (
                      <option key={time} value={time}>
                        {time}
                      </option>
                    ))}
                  </select>
                </div>
  
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Shipping Zones
                  </label>
                  <div className="grid grid-cols-2 gap-2">
                    {["Ife Central", "Ife East", "Ife North", "Ife South"].map(
                      zone => (
                        <label
                          key={zone}
                          className={`flex items-center ${
                            data.shippingZones.includes(zone)
                              ? "text-black font-semibold"
                              : "text-gray-700"
                          }`}
                        >
                          <input
                            type="checkbox"
                            checked={data.shippingZones.includes(zone)}
                            onChange={e => {
                              const zones = e.target.checked
                                ? [...data.shippingZones, zone]
                                : data.shippingZones.filter(z => z !== zone);
                              onUpdate({ shippingZones: zones });
                            }}
                            className="mr-2"
                          />
                          <span className="text-sm">{zone}</span>
                        </label>
                      )
                    )}
                  </div>
                </div>
              </div>
            </div>
  
            {/* Return Policies */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Return & Refund Policies
              </h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Return Window
                  </label>
                  <select
                    value={data.returnWindow}
                    onChange={e => onUpdate({ returnWindow: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Select return window</option>
                    {returnWindows.map(window => (
                      <option key={window} value={window}>
                        {window}
                      </option>
                    ))}
                  </select>
                </div>
  
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Return Conditions
                  </label>
                  <textarea
                    value={data.returnConditions}
                    onChange={e => onUpdate({ returnConditions: e.target.value })}
                    rows={3}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Describe conditions for returns (e.g., items must be unused, in original packaging...)"
                  />
                </div>
  
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Refund Processing Time
                  </label>
                  <select
                    value={data.refundTime}
                    onChange={e => onUpdate({ refundTime: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Select refund time</option>
                    <option value="3-5 days">3-5 business days</option>
                    <option value="5-7 days">5-7 business days</option>
                    <option value="7-10 days">7-10 business days</option>
                  </select>
                </div>
              </div>
            </div>
  
            {/* Custom Policies */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Additional Policies
              </h3>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Custom Store Policies
                </label>
                <textarea
                  value={data.customPolicies}
                  onChange={e => onUpdate({ customPolicies: e.target.value })}
                  rows={4}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Add any additional policies specific to your store..."
                />
              </div>
            </div>
          </div>
  
          <div className="flex justify-between mt-8">
            <button
              onClick={onPrev}
              className="flex items-center px-6 py-2 text-gray-600 hover:text-gray-800"
            >
              <ChevronLeft className="w-5 h-5 mr-2" />
              Previous
            </button>
            <button
              onClick={onNext}
              className="bg-black hover:bg-black/80 cursor-pointer text-white px-6 py-2 rounded-lg flex items-center"
            >
              Continue
              <ChevronRight className="w-5 h-5 ml-2" />
            </button>
          </div>
        </div>
      </div>
    );
  };