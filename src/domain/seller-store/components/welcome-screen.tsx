
import React from "react";
import { ChevronRight, Store, CreditCard, BarChart3 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

export const WelcomeScreen: React.FC<{ onNext: () => void }> = ({ onNext }) => {
    return (
      <div className="text-center py-16">
        <div className="max-w-2xl mx-auto">
          <Store className="w-16 h-16 mx-auto text-black mb-8" />
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Welcome to Your Store Journey
          </h2>
          <p className="text-sm text-gray-600 mb-8">
            Let&apos;s create your professional online store in just a few simple
            steps. We&apos;ll guide you through everything you need to start selling.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
            <div className="bg-white p-6 rounded-md border border-gray-100 flex items-center justify-center flex-col">
              <Store className="w-8 h-8 text-black mb-4" />
              <h3 className="font-semibold mb-2">Professional Setup</h3>
              <p className="text-gray-600">
                Create a professional storefront with custom branding
              </p>
            </div>
            <div className="bg-white p-6 rounded-md border border-gray-100 flex items-center justify-center flex-col">
              <CreditCard className="w-8 h-8 text-green-600 mb-4" />
              <h3 className="font-semibold mb-2">Secure Payments</h3>
              <p className="text-gray-600">
                Accept payments safely with integrated payment processing
              </p>
            </div>
            <div className="bg-white p-6 rounded-md border border-gray-100 flex items-center justify-center flex-col">
              <BarChart3 className="w-8 h-8 text-purple-600 mb-4" />
              <h3 className="font-semibold mb-2">Analytics & Growth</h3>
              <p className="text-gray-600">
                Track performance and grow your business with insights
              </p>
            </div>
          </div>
          <Button
            onClick={onNext}
            className="bg-black hover:bg-black/80 cursor-pointer text-white !px-8 py-3 rounded-lg font-semibold flex items-center mx-auto"
          >
            Start Creating Your Store
            <ChevronRight className="w-5 h-5 ml-2" />
          </Button>
        </div>
      </div>
    );
  };