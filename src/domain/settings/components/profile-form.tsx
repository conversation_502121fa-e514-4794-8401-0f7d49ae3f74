"use client";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useState } from "react";

interface ProfileFormProps {
  initialData: {
    name: string;
    email: string;
    phone: string;
    role: string;
    currentPassword?: string;
    newPassword?: string;
    confirmPassword?: string;
  };
}

export default function ProfileForm({ initialData }: ProfileFormProps) {
  const [formData, setFormData] = useState(initialData);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSave = () => {
    // Handle form submission
    console.log("Form submitted:", formData);
  };

  return (
    <div>
      {/* Personal Information Section */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold text-gray-900 mb-6">
          Personal Information
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="name" className="text-sm font-medium text-gray-700">
              Name
            </Label>
            <Input
              id="name"
              type="text"
              value={formData.name}
              onChange={e => handleInputChange("name", e.target.value)}
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <Label
              htmlFor="email"
              className="text-sm font-medium text-gray-700"
            >
              Email
            </Label>
            <Input
              id="email"
              type="email"
              placeholder="Enter your email"
              value={formData.email}
              onChange={e => handleInputChange("email", e.target.value)}
              className="w-full"
            />
          </div>
        </div>

        <div className="mt-6">
          <div className="space-y-2">
            <Label
              htmlFor="phone"
              className="text-sm font-medium text-gray-700"
            >
              Phone
            </Label>
            <Input
              id="phone"
              type="tel"
              placeholder="Enter your phone"
              value={formData.phone}
              onChange={e => handleInputChange("phone", e.target.value)}
              className="w-full md:w-1/2"
            />
          </div>
        </div>
      </div>

      {/* Change Password Section */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold text-gray-900 mb-6">
          Change Password
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label
              htmlFor="currentPassword"
              className="text-sm font-medium text-gray-700"
            >
              Current Password
            </Label>
            <Input
              id="currentPassword"
              type="password"
              placeholder="Enter your current password"
              value={formData.currentPassword}
              onChange={e =>
                handleInputChange("currentPassword", e.target.value)
              }
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <Label
              htmlFor="newPassword"
              className="text-sm font-medium text-gray-700"
            >
              New Password
            </Label>
            <Input
              id="newPassword"
              type="password"
              placeholder="Enter your new password"
              value={formData.newPassword}
              onChange={e => handleInputChange("newPassword", e.target.value)}
              className="w-full"
            />
          </div>
        </div>

        <div className="mt-6">
          <div className="space-y-2">
            <Label
              htmlFor="confirmPassword"
              className="text-sm font-medium text-gray-700"
            >
              Confirm Password
            </Label>
            <Input
              id="confirmPassword"
              type="password"
              placeholder="Confirm your new password"
              value={formData.confirmPassword}
              onChange={e =>
                handleInputChange("confirmPassword", e.target.value)
              }
              className="w-full md:w-1/2"
            />
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div>
        <Button
          onClick={handleSave}
          className="bg-black hover:bg-gray-800 text-white px-8 py-2 rounded-md"
        >
          Save
        </Button>
      </div>
    </div>
  );
}
