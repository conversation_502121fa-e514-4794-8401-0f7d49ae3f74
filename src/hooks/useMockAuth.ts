// src/hooks/useMockAuth.ts

import { useState, useCallback, useEffect } from "react";
import { mockUser } from "@/mocks/mockUser";
import type { User, UserRole } from "@/types/user";
import { useRouter } from "next/navigation";

const STORAGE_KEY = "markket_user";

export function useMockAuth() {
  const [user, setUser] = useState<User | null>(null);
  const router = useRouter();

  const login = useCallback(() => {
    setUser(mockUser);
    localStorage.setItem(STORAGE_KEY, JSON.stringify(mockUser));
  }, []);

  const logout = useCallback(() => {
    setUser(null);
    localStorage.removeItem(STORAGE_KEY);
  }, []);

  const switchRole = useCallback(
    (role: UserRole) => {
      if (!user?.roles.includes(role)) return;
      const updatedUser = { ...user, current_role: role };
      setUser(updatedUser);
      localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedUser));

      router.push("/dashboard");
    },
    [user, router]
  );

  useEffect(() => {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      setUser(JSON.parse(stored));
    }
  }, []);

  return {
    user,
    isAuthenticated: !!user,
    login,
    logout,
    switchRole,
  };
}
