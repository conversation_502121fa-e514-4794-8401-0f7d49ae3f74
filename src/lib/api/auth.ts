import type { RegisterPayload } from "@/types/user";
import axios from "axios";

const BASE_URL = "https://markket-be.onrender.com/api/v1/auth";

export const registerUser = async (data: RegisterPayload) => {
  return axios.post(`${BASE_URL}/register`, data);
};

export const loginUser = async (username: string, password: string) => {
  const payload = new URLSearchParams();
  payload.append("grant_type", "password");
  payload.append("username", username);
  payload.append("password", password);

  return axios.post(`${BASE_URL}/login`, payload, {
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
  });
};
