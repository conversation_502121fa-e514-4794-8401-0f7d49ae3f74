export type UserRole = "buyer" | "seller" | "rider";

export interface User {
  id: string;
  first_name: string;
  // last_name: string;
  email: string;
  name: string;
  phone?: string;
  date_of_birth?: string;
  avatar_url?: string;
  roles: UserRole[];
  current_role: UserRole;
  is_new_user: boolean;
}

export type RegisterPayload = {
  email: string;
  first_name: string;
  last_name: string;
  gender: "male" | "female" | "other";
  date_of_birth: string;
  can_share_user_data: boolean;
  profile_image: string;
  residential_address: string;
  phone_number: string;
  role: string;
  password: string;
};
